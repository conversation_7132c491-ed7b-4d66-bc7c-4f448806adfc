import dedent from "dedent";
import { v4 as uuid } from "uuid";
import { CompletionContext } from "../CompletionContext";

/**
 * 创建用于测试的文档上下文
 * 使用特殊字符 ║ 标记光标位置
 *
 * @param literals - 模板字符串数组
 * @param placeholders - 插值参数
 * @returns 补全上下文对象
 */
export function documentContext(literals: TemplateStringsArray, ...placeholders: any[]): CompletionContext {
  const doc = dedent(literals, ...placeholders);
  return new CompletionContext({
    filepath: uuid(),
    language: "",
    text: doc.replace(/║/, ""),
    position: doc.indexOf("║"),
    line: 0,
    col: 0,
    stopWords: ["", "", ""],
    indentation: "",
  });
}

/**
 * 创建用于测试的行内补全内容
 * 使用特殊字符标记补全范围：
 * ├ - 补全开始
 * ┤ - 补全结束
 * ┴ - 缩进占位符（用于最后一行）
 *
 * @param literals - 模板字符串数组
 * @param placeholders - 插值参数
 * @returns 处理后的补全内容
 */
export function inline(literals: TemplateStringsArray, ...placeholders: any[]): string {
  const inline = dedent(literals, ...placeholders);
  return inline.slice(inline.indexOf("├") + 1, inline.lastIndexOf("┤"));
}
