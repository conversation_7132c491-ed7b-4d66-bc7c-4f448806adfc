import Parser = require('web-tree-sitter');
import * as path from 'path';
import { logger } from "../../logger";
import { getParser } from "../../syntax/parser";
import CodeObjectIndexSystem from '../codeObjectIndexSystem';
import RelativeCodeObject from '../relativeCodeObject';
import { IRelativeCodeFinder, SubObject, SubObjectType } from '../types';
import { UpdateObject } from '../updateObject';
import { PythonField, PythonFile, PythonModule, PythonObject } from './types';
import fs = require('fs');

// Helper interfaces
interface IdentifierContext {
  identifier: string;
  subscriptBase?: string; // Base object being subscripted (e.g., 'a' in a['b'])
  subscriptKey?: string; // Key/index being used (e.g., 'b' in a['b'])
  contextNode: Parser.SyntaxNode;
  isMemberAccess: boolean;
  isFunctionCall: boolean;
  isCollectionAccess: boolean;
  scopeNode: Parser.SyntaxNode;
}

interface TypeInfo {
  packageName: string;
  typeName: string;
}

interface Position {
  row: number;
  column: number;
}

/**
 * Python code interpreter
 * Interprets Python code files using abstract syntax trees and stores main syntax objects
 * in the code object index system. For related queries, performs global syntax object
 * search to implement cross-file code association.
 */
export class PythonParser implements IRelativeCodeFinder {
  private parser: Parser | null;

  private codeObjectIndexSystem: CodeObjectIndexSystem;

  private initializationPromise: Promise<void> | null = null;

  public constructor(codeObjectIndexSystem: CodeObjectIndexSystem) {
    this.parser = null;
    this.codeObjectIndexSystem = codeObjectIndexSystem;
  }

  // eslint-disable-next-line @typescript-eslint/member-ordering
  public initialize(workspaceRootPath?: string): Promise<void> {
    if (!this.initializationPromise) {
      this.initializationPromise = this.InitPythonParser(workspaceRootPath).then(() => {
        logger.info("InitPythonParser complete");
      });
    }
    return this.initializationPromise;
  }

  public async InitPythonParser(workspaceRootPath?: string): Promise<void> {
    logger.info("InitPythonParser start");
    this.parser = await getParser("python")
    return;
  }

  public async ParseFile(fileName: string, codeFileContent: string): Promise<void> {
    logger.debug(`[PythonParser] Parse Python Code in file: ${fileName}`);
    // logger.debug(`[PythonParser] Content: ${codeFileContent}`);
    this.parsePythonCode(fileName, codeFileContent, null);
  }

  public FindRelativeObject(
    fileContent: string,
    filePath: string,
    languageId: string,
    line: number,
    column: number
  ): RelativeCodeObject | null {
    const codeFileContent = fileContent;
    const codeFilePath = filePath;

    if (!codeFileContent?.trim()) {
      logger.warn('[cf rlcc] FindRelativeObjects skip, codeFileContent is null or blank');
      return null;
    }

    if (!this.parser) {
      return null;
    }

    const tree = this.parser.parse(codeFileContent);
    const rootNode = tree.rootNode;

    // Update file parsing
    const currFile = this.parsePythonCode(codeFilePath, codeFileContent, rootNode);
    if (!currFile) {
      logger.debug('[cf rlcc] FindRelativeObjects skip, parsePythonCode return null');
      return null;
    }

    // Find node at cursor position
    const cursorNode = this.findNodeAtPosition(rootNode, line, column);
    if (!cursorNode) {
      logger.warn('[cf rlcc] FindRelativeObjects skip, cursor node not found');
      return null;
    }

    // Extract identifier context
    const context = this.extractIdentifierContext(cursorNode);
    if (!context?.identifier) {
      logger.warn('[cf rlcc] FindRelativeObjects skip, identifier context not found');
      return null;
    }

    // Find type information
    const typeInfo = this.findTypeInfo(context, rootNode, currFile.getImportModules(), currFile.getPackageName());
    if (!typeInfo) {
      logger.warn('[cf rlcc] FindRelativeObjects skip, type info not found');
      return null;
    }

    // Query object from index system
    return this.codeObjectIndexSystem.queryPythonRelativeObject(
      currFile.getPackageName(),
      typeInfo.packageName,
      typeInfo.typeName
    );
  }

  private parsePythonCode(
    fileName: string,
    codeFileContent: string,
    rootNode: Parser.SyntaxNode | null
  ): PythonFile | null {
    logger.debug(`[PythonParser] Parse Python Code in file: ${fileName}`);
    // logger.debug(`[PythonParser] Content: ${codeFileContent}`);

    if (!codeFileContent?.trim()) {
      logger.warn('[cf rlcc] parsePythonCode skip, content is empty:', fileName);
      return null;
    }

    if (!this.parser) {
      return null;
    }

    if (!rootNode) {
      const tree = this.parser.parse(codeFileContent);
      rootNode = tree.rootNode;
    }

    const packageName = this.determinePackageName(fileName);
    const importModules: PythonModule[] = [];
    const objects: PythonObject[] = [];
    const objectsToUpdate: UpdateObject[] = [];

    for (let i = 0; i < rootNode.childCount; i++) {
      const node = rootNode.child(i);
      if (!node) {
        continue;
      }

      switch (node.type) {
        case 'import_statement': {
          for (let j = 0; j < node.childCount; j++) {
            const childNode = node.child(j);
            if (!childNode) {
              continue;
            }

            if (childNode.type === 'dotted_name') {
              // Handle: import xxx
              const moduleName = childNode.text.trim();
              importModules.push(new PythonModule('', moduleName, ''));
            } else if (childNode.type === 'aliased_import') {
              // Handle: import xxx as yyy
              const moduleName = childNode.child(0)?.text.trim() || '';
              const alias = childNode.child(2)?.text.trim() || '';
              importModules.push(new PythonModule('', moduleName, alias));
            }
          }
          break;
        }

        case 'import_from_statement': {
          // Handle: from xxx import yyy as zzz
          let fromPackage = '';
          let k = 0;
          for (let j = 0; j < node.childCount; j++) {
            const childNode = node.child(j);
            if (!childNode) {
              continue;
            }

            if (childNode.type === 'dotted_name') {
              if (k === 0) {
                fromPackage = childNode.text.trim();
                k++;
              } else {
                const moduleName = childNode.text.trim();
                importModules.push(new PythonModule(fromPackage, moduleName, ''));
              }
            } else if (childNode.type === 'aliased_import') {
              const moduleName = childNode.child(0)?.text.trim() || '';
              const alias = childNode.child(2)?.text.trim() || '';
              importModules.push(new PythonModule(fromPackage, moduleName, alias));
            }
          }
          break;
        }

        case 'expression_statement': {
          const expr = node.child(0);
          if (expr?.type === 'assignment') {
            let left = null;
            let right = null;
            if (expr.childCount >= 3) {
              left = expr.child(0);
              right = expr.child(2);
            } else {
              continue;
            }

            if (left?.type === 'identifier' && right) {
              let objectType = '';
              const subObjects: SubObject[] = [];

              // Analyze the right-hand side to determine type
              if (right.type === 'dictionary') {
                objectType = 'dict';
                // Analyze dictionary values for their types
                for (let j = 0; j < right.childCount; j++) {
                  const pair = right.child(j);
                  if (pair?.type === 'pair') {
                    const key = pair.child(0);
                    const value = pair.child(2);

                    if (value?.type === 'call') {
                      const funcName = value.child(0)?.text;
                      // Check if the value is a class instantiation
                      for (const importModule of importModules) {
                        if (
                          funcName === importModule.getModuleName() ||
                          funcName === importModule.getAsName()
                        ) {
                          subObjects.push({
                            name: key?.text.replace(/['"]/g, '') || '',
                            type: SubObjectType.FIELD,
                            packagePath: importModule.getFromPackage(),
                            returns: [],
                          });
                          break;
                        }
                      }
                    } else if (value?.type === 'identifier') {
                      // Handle identifier case - could be an object from another file
                      for (const importModule of importModules) {
                        if (
                          value.text === importModule.getModuleName() ||
                          value.text === importModule.getAsName()
                        ) {
                          subObjects.push({
                            name: key?.text.replace(/['"]/g, '') || '',
                            type: SubObjectType.FIELD,
                            packagePath: importModule.getFromPackage(),
                            returns: [],
                          });
                          break;
                        }
                      }
                    }
                  }
                }
              } else if (right.type === 'list') {
                objectType = 'list';
                // Analyze list elements for their types
                for (let j = 0; j < right.childCount; j++) {
                  const element = right.child(j);
                  if (element?.type === 'call') {
                    const funcName = element.child(0)?.text;
                    // Check if the element is a class instantiation
                    for (const importModule of importModules) {
                      if (
                        funcName === importModule.getModuleName() ||
                        funcName === importModule.getAsName()
                      ) {
                        subObjects.push({
                          name: `-{j}`,
                          type: SubObjectType.FIELD,
                          packagePath: importModule.getFromPackage(),
                          returns: [],
                        });
                        break;
                      }
                    }
                  }
                }
              } else if (right.type === 'call') {
                // Check if it's a class instantiation
                const funcName = right.child(0)?.text;
                for (const importModule of importModules) {
                  if (
                    funcName === importModule.getModuleName() ||
                    funcName === importModule.getAsName()
                  ) {
                    objectType = 'instance';
                    objectsToUpdate.push(
                      new UpdateObject('', objectType, left.text, `${left.text} = ${right.text}`, [
                        {
                          name: left.text,
                          type: SubObjectType.FIELD,
                          packagePath: importModule.getFromPackage(),
                          returns: [],
                        },
                      ])
                    );
                    break;
                  }
                }
              }

              // Add to objectsToUpdate if it's a container type
              if ((objectType === 'dict' || objectType === 'list') && subObjects.length > 0) {
                objectsToUpdate.push(
                  new UpdateObject(
                    '',
                    objectType,
                    left.text,
                    `${left.text} = ${right.text}`,
                    subObjects
                  )
                );
              }
            }
          }
          break;
        }

        case 'class_definition':
        case 'function_definition':
        case 'enum_definition': {
          const obj = this.extractPythonObject(node, importModules);
          objects.push(obj);
          const subObjects: SubObject[] = [];

          // 将返回类型转换为SubObject
          // 通常obj为func时，returns为该函数的返回类型列表，这里是将返回类型转换为SubObject
          for (const ret of obj.getReturns()) {
            subObjects.push({
              name: ret.getModuleName(),
              type: SubObjectType.RETURN_TYPE,
              packagePath: ret.getFromPackage(),
              returns: [],
            });
          }

          // 将子对象转换为SubObject
          // 通常obj为class时，children为该类的成员方法列表，这里即将成员方法转换为SubObject
          for (const child of obj.getChildren()) {
            subObjects.push({
              name: child.getObjectName(),
              type: SubObjectType.METHOD,
              //parameters: child.getParameters(), //暂时不支持方法输出参数的储存
              returns: child.getReturns().map(ret => ({
                name: ret.getModuleName(),
                type: SubObjectType.RETURN_TYPE,
                packagePath: ret.getFromPackage(),
                returns: [],
              })),
            });
          }

          objectsToUpdate.push(
            new UpdateObject(
              '', //packageName,
              obj.getObjectType(),
              obj.getObjectName(),
              obj.getSimpleText(),
              subObjects
            )
          );
          break;
        }
      }
    }

    // 从全路径名称中将文件名提取出来，作为后续进入索引系统时将文件名加入到packageName中
    const fileNameWithoutExt = path.basename(fileName, path.extname(fileName));
    let targetPackageName = '';
    if (packageName === '') {
      targetPackageName = fileNameWithoutExt;
    } else {
      targetPackageName = packageName + '.' + fileNameWithoutExt;
    }

    this.codeObjectIndexSystem.updateByFile(targetPackageName, fileName, objectsToUpdate);

    return new PythonFile(packageName, importModules, objects);
  }

  private determinePackageName(filePath: string): string {
    // Convert to platform-specific path
    const normalizedPath = filePath.replace(/\\/g, path.sep);
    const parts = normalizedPath.split(path.sep);

    // Common Python project root indicators
    const rootIndicators = [
      'setup.py',
      'pyproject.toml',
      'requirements.txt',
      'src',
      'tests',
      '.git',
      '.venv',
      'venv',
    ];

    // Start from the file's directory and traverse up
    const packageParts: string[] = [];
    let currentPath = parts.slice(0, -1).join(path.sep); // Remove filename
    let rootFound = false;
    let rootPath = '';

    // First find the project root
    while (
      currentPath &&
      currentPath.length > 1 &&
      !path.parse(currentPath).root.includes(currentPath)
    ) {
      // Check if we've reached project root
      const hasRootIndicator = rootIndicators.some(indicator =>
        fs.existsSync(path.join(currentPath, indicator))
      );

      if (hasRootIndicator) {
        rootFound = true;
        rootPath = currentPath;
        break;
      }

      currentPath = path.dirname(currentPath);
    }

    // If we found the root, collect package names from root to file
    if (rootFound && rootPath) {
      const pathFromRoot = filePath.substring(rootPath.length + 1);
      const pathParts = pathFromRoot.split(path.sep);

      // Remove the filename
      pathParts.pop();

      // Keep track of the current directory path as we build up the package name
      let currentDir = rootPath;
      let isFirstLevel = true;
      let foundFirstPackage = false;

      // Build package name from remaining parts
      for (const part of pathParts) {
        // Skip if the directory is clearly not a package
        if (part.startsWith('.') || part === 'tests' || part === 'test') {
          continue;
        }

        // Update current directory
        currentDir = path.join(currentDir, part);

        // Check if directory is a Python package
        const initPath = path.join(currentDir, '__init__.py');
        const hasPythonFiles = fs
          .readdirSync(currentDir)
          .some(file => file.endsWith('.py') && file !== '__init__.py');

        // Consider as package if:
        // 1. Has __init__.py, or
        // 2. Is first level directory under root and contains Python files
        if (fs.existsSync(initPath) || (isFirstLevel && hasPythonFiles)) {
          foundFirstPackage = true;
        }

        // Once we've found the first package, include all subsequent directories
        if (foundFirstPackage) {
          packageParts.push(part);
        }

        isFirstLevel = false;
      }
    }

    return packageParts.join('.');
  }

  private extractMethodInfo(
    methodNode: Parser.SyntaxNode,
    importModules: PythonModule[]
  ): [string, PythonModule[], string, PythonField[]] {
    const returns: PythonModule[] = [];
    const selfFields: PythonField[] = [];
    let simpleText = '';
    let methodName = '';

    // Get method name and parameters
    const nameNode = this.getChildByTypeForNode(methodNode, 'identifier');
    const paramsNode = this.getChildByTypeForNode(methodNode, 'parameters');
    const decoratorNode = this.getChildByTypeForNode(methodNode, 'decorator');
    if (nameNode && paramsNode) {
      simpleText = `def ${nameNode.text}${paramsNode.text}`;
      methodName = nameNode.text;
    }

    const returnNode = this.getChildByTypeForNode(methodNode, 'type');
    // Process return type annotation if present
    if (returnNode) {
      const returnText = returnNode.text.trim();

      // Check if it's a tuple return type
      if (returnText.startsWith('(') && returnText.endsWith(')')) {
        // Multiple return types
        const types = returnText
          .slice(1, -1)
          .split(',')
          .map(t => t.trim());
        const processedTypes = [];
        for (const type of types) {
          // Extract base type if it's a container type (e.g. Iterator[str] -> Iterator)
          const bracketIndex = type.indexOf('[');
          const baseType = bracketIndex > -1 ? type.substring(0, bracketIndex) : type;

          let processedType = type;
          let moduleName = '';
          let fromPackage = '';
          let isImported = false;

          // Try to match base type with imported modules
          for (const importedModule of importModules) {
            if (
              baseType === importedModule.getModuleName() ||
              baseType === importedModule.getAsName()
            ) {
              // Keep the generic part if it exists
              const genericPart = bracketIndex > -1 ? type.substring(bracketIndex) : '';
              processedType = `${importedModule.getFromPackage()}.${importedModule.getModuleName()}${genericPart}`;
              moduleName = importedModule.getModuleName();
              fromPackage = importedModule.getFromPackage();
              isImported = true;
              break;
            }
          }
          processedTypes.push(processedType);
          if (isImported) {
            returns.push(new PythonModule(fromPackage, moduleName, ''));
          } else {
            returns.push(new PythonModule('', processedType, ''));
          }
        }

        // Add tuple return types to simpleText
        //simpleText += ` -> (${processedTypes.join(', ')}):`;
        simpleText += ` -> ${returnText}:`;
      } else {
        // Single return type
        let returnType = returnText;
        let isImported = false;

        // Extract base type if it's a container type (e.g. Iterator[str] -> Iterator)
        const bracketIndex = returnType.indexOf('[');
        const baseType = bracketIndex > -1 ? returnType.substring(0, bracketIndex) : returnType;

        // Try to match return type with imported modules
        for (const importedModule of importModules) {
          if (
            baseType === importedModule.getModuleName() ||
            baseType === importedModule.getAsName()
          ) {
            returnType = `${importedModule.getFromPackage()}.${importedModule.getModuleName()}`;
            // Add to returns array
            returns.push(
              new PythonModule(importedModule.getFromPackage(), importedModule.getModuleName(), '')
            );
            isImported = true;
            break;
          }
        }
        if (!isImported) {
          returns.push(new PythonModule('', baseType, ''));
        }

        // Add single return type to simpleText
        simpleText += ` -> ${returnType}:`;
      }
    } else {
      simpleText += `:`;
    }

    // For __init__ methods, copy self field assignments to simpleText
    if (methodName === '__init__') {
      const blockNode = this.getChildByTypeForNode(methodNode, 'block');
      if (blockNode) {
        for (let i = 0; i < blockNode.childCount; i++) {
          const stmt = blockNode.child(i);
          if (!stmt) {
            continue;
          }

          // Look for self.xxx assignments
          if (stmt.type === 'expression_statement') {
            const expr = stmt.child(0);
            if (expr?.type === 'assignment') {
              const left = expr.child(0);
              if (left?.text.startsWith('self.')) {
                simpleText += `\n  ${left.text} = ${expr.child(2)?.text || ''}`;
              }
            }
          }
        }
      }
    } else {
      simpleText += '\n pass';
    }

    // Process method body for returns and self fields
    /*const blockNode = this.getChildByTypeForNode(methodNode, 'block');
    if (blockNode) {
      for (let i = 0; i < blockNode.childCount; i++) {
        const stmt = blockNode.child(i);
        if (!stmt) {
          continue;
        }

        // Look for self.xxx assignments
        if (stmt.type === 'expression_statement') {
          const expr = stmt.child(0);
          if (expr?.type === 'assignment') {
            const left = expr.child(0);
            if (left?.text.startsWith('self.')) {
              const fieldName = left.text.substring(5);
              selfFields.push(new PythonField(fieldName, ''));
            }
          }
        }

        // Look for return statements
        if (stmt.type === 'return_statement') {
          const returnExpr = stmt.child(0);
          if (returnExpr) {
            returns.push(new PythonField('return', this.inferType(returnExpr)));
          }
        }
      }
    }*/

    return [methodName, returns, simpleText, selfFields];
  }

  private extractPythonObject(
    node: Parser.SyntaxNode,
    importModules: PythonModule[]
  ): PythonObject {
    let simpleText = '';
    const fields: PythonField[] = [];
    const children: PythonObject[] = [];
    const returns: PythonModule[] = [];
    let objectName = '';
    let objectType = '';

    switch (node.type) {
      case 'class_definition': {
        // Get class name and base classes
        const classNameNode = this.getChildByTypeForNode(node, 'identifier');
        objectName = classNameNode?.text || '';
        objectType = 'class';
        simpleText = `class ${objectName}`;

        const baseListNode = this.getChildByTypeForNode(node, 'argument_list');
        if (baseListNode) {
          simpleText += baseListNode.text;
        }
        simpleText += ':';

        // Process class body
        const blockNode = this.getChildByTypeForNode(node, 'block');
        if (blockNode) {
          for (let j = 0; j < blockNode.childCount; j++) {
            const bodyNode = blockNode.child(j);
            if (!bodyNode) {
              continue;
            }

            if (bodyNode.type === 'function_definition') {
              // Process method
              const [methodName, methodReturns, methodSimpleText, selfFields] =
                this.extractMethodInfo(bodyNode, importModules);

              // Create method PythonObject
              const methodObj = new PythonObject(
                methodName,
                'func',
                methodSimpleText,
                [], // methods don't have fields
                [], // methods don't have children
                methodReturns
              );
              children.push(methodObj);

              // Add method signature to class simple text
              simpleText += `\n    ${methodSimpleText}`;
            } else if (bodyNode.type === 'expression_statement') {
              // Handle class member variable declarations
              const expr = bodyNode.child(0);
              if (expr) {
                if (expr.type === 'assignment') {
                  // Handle simple assignments: h = 1
                  const left = expr.child(0);
                  const right = expr.child(2);
                  if (left && right) {
                    simpleText += `\n    ${expr.text}`;
                  }
                } else if (expr.type === 'typed_identifier') {
                  // Handle type annotations without value: model: str
                  simpleText += `\n    ${expr.text}`;
                } else if (expr.type === 'annotated_assignment') {
                  // Handle type annotations with value: max_tokens: Optional[int] = 2048
                  simpleText += `\n    ${expr.text}`;
                }
              }
            } else if (bodyNode.type === 'decorated_definition') {
              // Handle static variables with @property decorator
              const decorator = this.getChildByTypeForNode(bodyNode, 'decorator');
              if (decorator?.text.includes('@property')) {
                const varNameNode = this.getChildByTypeForNode(bodyNode, 'identifier');
                if (varNameNode) {
                  // fields.push(new PythonField(varNameNode.text.trim(), ''));
                }
              }
            }
          }
        }
        break;
      }

      case 'function_definition': {
        // Get function info
        const [methodName, methodReturns, methodSimpleText, methodFields] = this.extractMethodInfo(
          node,
          importModules
        );
        returns.push(...methodReturns);
        simpleText = methodSimpleText;
        fields.push(...methodFields);
        objectName = methodName;
        objectType = 'func';
        break;
      }

      case 'enum_definition': {
        // Get enum name
        const enumNameNode = this.getChildByTypeForNode(node, 'identifier');
        objectName = enumNameNode?.text || '';
        simpleText = `class ${objectName}(Enum):`;

        // Process enum members
        const enumBlock = this.getChildByTypeForNode(node, 'block');
        if (enumBlock) {
          for (let i = 0; i < enumBlock.childCount; i++) {
            const member = enumBlock.child(i);
            if (!member) {
              continue;
            }

            if (member.type === 'expression_statement') {
              const expr = member.child(0);
              if (expr?.type === 'assignment') {
                const left = expr.child(0);
                if (left) {
                  // fields.push(new PythonField(left.text, 'enum_member'));
                }
              }
            }
          }
        }
        break;
      }
    }

    return new PythonObject(objectName, objectType, simpleText, fields, children, returns);
  }

  private getChildByTypeForNode(node: Parser.SyntaxNode, type: string): Parser.SyntaxNode | null {
    for (let i = 0; i < node.childCount; i++) {
      const child = node.child(i);
      if (child?.type === type) {
        return child;
      }
    }
    return null;
  }

  private findNodeAtPosition(
    rootNode: Parser.SyntaxNode,
    line: number,
    column: number
  ): Parser.SyntaxNode | null {
    if (!rootNode) {
      return null;
    }

    // Use breadth-first search to find the innermost node containing the cursor position
    let result: Parser.SyntaxNode | null = null;
    const queue: Parser.SyntaxNode[] = [rootNode];

    while (queue.length > 0) {
      const current = queue.shift();
      if (!current) {
        continue;
      }

      const start = current.startPosition;
      const end = current.endPosition;

      // Check if current node contains target position
      if (this.isPositionInNode(line, column, start, end)) {
        result = current;
        // Continue searching child nodes to find most precise match
        for (let i = 0; i < current.childCount; i++) {
          const child = current.child(i);
          if (child) {
            queue.push(child);
          }
        }
      }
    }

    return result;
  }

  private isPositionInNode(line: number, column: number, start: Position, end: Position): boolean {
    // Check if position is within node boundaries
    if (line < start.row || line > end.row) {
      return false;
    }
    if (line === start.row && column < start.column) {
      return false;
    }
    if (line === end.row && column > end.column) {
      return false;
    }
    return true;
  }

  private extractIdentifierContext(cursorNode: Parser.SyntaxNode): IdentifierContext {
    let current: Parser.SyntaxNode | null = cursorNode;
    let isMemberAccess = false;
    let isFunctionCall = false;
    let isCollectionAccess = false;
    let identifier: string | null = null;
    let subscriptBase: string | undefined;
    let subscriptKey: string | undefined;
    let scopeNode: Parser.SyntaxNode | null = null;

    // Traverse up the tree to find relevant context
    while (current) {
      const nodeType = current.type;

      switch (nodeType) {
        case 'identifier':
          if (!identifier) {
            identifier = current.text;
          }
          break;
        case '.':
          // For dot operator, check the previous node
          isMemberAccess = false;
          if (!identifier) {
            const prevNode = current.previousSibling;
            if (prevNode) {
              switch (prevNode.type) {
                case 'identifier':
                  identifier = prevNode.text;
                  break;
                case 'subscript':
                  {
                    const subscriptInfo = this.extractSubscriptIdentifier(prevNode);
                    if (subscriptInfo) {
                      identifier = subscriptInfo.base;
                      subscriptBase = subscriptInfo.base;
                      subscriptKey = subscriptInfo.key;
                    }
                  }
                  isCollectionAccess = true;
                  break;
                case 'call':
                  identifier = this.extractCallIdentifier(prevNode);
                  isFunctionCall = true;
                  break;
                case 'attribute':
                  identifier = this.extractAttributeIdentifier(prevNode);
                  isMemberAccess = true;
                  break;
                case 'string':
                case 'integer':
                case 'float':
                  identifier = prevNode.text;
                  break;
                case 'parenthesized_expression':
                  {
                    const innerNode = prevNode.child(1); // Skip opening parenthesis
                    if (innerNode) {
                      switch (innerNode.type) {
                        case 'identifier':
                          identifier = innerNode.text;
                          break;
                        case 'call':
                          identifier = this.extractCallIdentifier(innerNode);
                          isFunctionCall = true;
                          break;
                        case 'subscript':
                          {
                            const subscriptInfo = this.extractSubscriptIdentifier(innerNode);
                            if (subscriptInfo) {
                              identifier = subscriptInfo.base;
                              subscriptBase = subscriptInfo.base;
                              subscriptKey = subscriptInfo.key;
                            }
                          }
                          isCollectionAccess = true;
                          break;
                      }
                    }
                  }
                  break;
              }
            }
          }
          break;
        case '(':
          // For parenthesis, check the previous node
          isFunctionCall = true;
          if (!identifier) {
            const prevNode = current.previousSibling;
            if (prevNode) {
              switch (prevNode.type) {
                case 'identifier':
                  identifier = prevNode.text;
                  break;
                case 'attribute':
                  identifier = this.extractAttributeIdentifier(prevNode);
                  isMemberAccess = true;
                  break;
                case 'subscript':
                  {
                    const subscriptInfo = this.extractSubscriptIdentifier(prevNode);
                    if (subscriptInfo) {
                      identifier = subscriptInfo.base;
                      subscriptBase = subscriptInfo.base;
                      subscriptKey = subscriptInfo.key;
                    }
                  }
                  isCollectionAccess = true;
                  break;
              }
            }
          }
          break;
        case ')':
          // For closing parenthesis, check if there's an opening parenthesis before
          if (!identifier) {
            const openParen = current.previousSibling;
            if (openParen?.type === '(') {
              const prevNode = openParen.previousSibling;
              if (prevNode) {
                switch (prevNode.type) {
                  case 'identifier':
                    identifier = prevNode.text;
                    isFunctionCall = true;
                    break;
                  case 'attribute':
                    identifier = this.extractAttributeIdentifier(prevNode);
                    isMemberAccess = true;
                    isFunctionCall = true;
                    break;
                  case 'subscript':
                    {
                      const subscriptInfo = this.extractSubscriptIdentifier(prevNode);
                      if (subscriptInfo) {
                        identifier = subscriptInfo.base;
                        subscriptBase = subscriptInfo.base;
                        subscriptKey = subscriptInfo.key;
                      }
                    }
                    isCollectionAccess = true;
                    isFunctionCall = true;
                    break;
                }
              }
            }
          }
          break;
        case 'attribute':
          isMemberAccess = false;
          if (!identifier) {
            identifier = this.extractAttributeIdentifier(current);
          }
          break;
        case 'call':
          isFunctionCall = true;
          if (!identifier) {
            identifier = this.extractCallIdentifier(current);
          }
          break;
        case 'subscript':
          isCollectionAccess = true;
          if (!identifier) {
            const subscriptInfo = this.extractSubscriptIdentifier(current);
            if (subscriptInfo) {
              identifier = subscriptInfo.base;
              subscriptBase = subscriptInfo.base;
              subscriptKey = subscriptInfo.key;
            }
          }
          break;
        case 'function_definition':
        case 'class_definition':
        case 'module':
          if (!scopeNode) {
            scopeNode = current;
          }
          break;
      }
      // Stop traversing if we found the scope node
      if (scopeNode) {
        break;
      }
      current = current.parent;
    }

    return {
      identifier: identifier || '',
      subscriptBase,
      subscriptKey,
      contextNode: cursorNode,
      isMemberAccess,
      isFunctionCall,
      isCollectionAccess,
      scopeNode: scopeNode || cursorNode,
    };
  }

  private extractAttributeIdentifier(attributeNode: Parser.SyntaxNode): string {
    for (let i = 0; i < attributeNode.childCount; i++) {
      const child = attributeNode.child(i);
      if (child?.type === 'identifier') {
        return child.text;
      }
    }
    return '';
  }

  private extractCallIdentifier(callNode: Parser.SyntaxNode): string | null {
    const function_ = callNode.child(0);
    if (function_?.type === 'identifier') {
      return function_.text;
    }
    return null;
  }

  private extractSubscriptIdentifier(
    subscriptNode: Parser.SyntaxNode
  ): { base: string; key: string } | null {
    const baseNode = subscriptNode.child(0);

    if (!baseNode || subscriptNode?.childCount < 1) {
      return null;
    }

    let base = '';
    let key = '';

    // Extract base identifier
    if (baseNode.type === 'identifier') {
      base = baseNode.text;
    } else if (baseNode.type === 'attribute') {
      base = this.extractAttributeIdentifier(baseNode);
    }

    // Extract subscript key/index
    for (let i = 1; i < subscriptNode.childCount; i++) {
      const child = subscriptNode.child(i);
      if (!child) {
        continue;
      }

      switch (child.type) {
        case 'string':
          key = child.text.replace(/['"]/g, ''); // Remove quotes
          break;
        case 'integer':
        case 'identifier':
          key = child.text;
          break;
      }
    }

    return base && key ? { base, key } : null;
  }

  private findTypeInfo(
    context: IdentifierContext,
    rootNode: Parser.SyntaxNode,
    importModules: PythonModule[],
    filePackagePath: string
  ): TypeInfo | null {
    // Add new case: Handle subscript access (e.g., models['gpt-4'])
    if (context.subscriptBase && context.subscriptKey) {
      // First find the base object type from imports
      for (const importedModule of importModules) {
        if (
          context.subscriptBase === importedModule.getModuleName() ||
          context.subscriptBase === importedModule.getAsName()
        ) {
          // Query the object from index system
          const cacheObject = this.codeObjectIndexSystem.queryPythonCacheObject(
            filePackagePath,
            importedModule.getFromPackage(),
            importedModule.getModuleName()
          );

          if (cacheObject?.subObjects) {
            // Find matching field in subObjects by name
            const field = cacheObject
              .subObjects
              .find(sub => sub.name === context.subscriptKey);
            if (field?.packagePath) {
              return {
                packageName: field.packagePath,
                typeName: field.name,
              };
            }
          }
        }
      }
    }

    // Check if identifier itself is a type in the index system
    if (!context.isMemberAccess) {
      // Check if identifier matches any imported modules
      for (const importedModule of importModules) {
        if (
          context.identifier === importedModule.getModuleName() ||
          context.identifier === importedModule.getAsName()
        ) {
          return {
            packageName: importedModule.getFromPackage(),
            typeName: importedModule.getModuleName(),
          };
        }
      }
    }

    // Check if in class method
    const classNode = this.findParentOfType(context.contextNode, 'class_definition');
    if (classNode && context.isMemberAccess) {
      const memberName = context.identifier;
      if (memberName.startsWith('self.')) {
        return this.findClassMemberType(classNode, memberName.substring(5), filePackagePath);
      }
    }

    // Check local scope variable assignments
    let typeInfo = this.findTypeFromAssignments(context, context.scopeNode, importModules, filePackagePath);
    if (typeInfo) {
      return typeInfo;
    }

    // Check global scope
    if (context.scopeNode !== rootNode) {
      typeInfo = this.findTypeFromAssignments(context, rootNode, importModules, filePackagePath);
      if (typeInfo) {
        return typeInfo;
      }
    }

    return null;
  }

  private findParentOfType(node: Parser.SyntaxNode, type: string): Parser.SyntaxNode | null {
    let current: Parser.SyntaxNode | null = node;
    while (current) {
      if (current.type === type) {
        return current;
      }
      current = current.parent;
    }
    return null;
  }

  private findClassMemberType(classNode: Parser.SyntaxNode, memberName: string, filePackagePath: string): TypeInfo | null {
    for (let i = 0; i < classNode.childCount; i++) {
      const child = classNode.child(i);
      if (!child) {
        continue;
      }

      if (
        child.type === 'function_definition' &&
        child.childForFieldName('name')?.text === '__init__'
      ) {
        // return this.findTypeFromAssignments('self.' + memberName, child, []);
        // return this.findTypeFromAssignments('self.' + memberName, child, []);
        // todo 验证下
        const context: IdentifierContext = {
          identifier: 'self.' + memberName,
          contextNode: child,
          isMemberAccess: true,
          isFunctionCall: false,
          isCollectionAccess: false,
          scopeNode: child
        };
        return this.findTypeFromAssignments(context, child, [], filePackagePath);

      }
    }
    return null;
  }

  private findTypeFromAssignments(
    context: IdentifierContext,
    scopeNode: Parser.SyntaxNode | null,
    importModules: PythonModule[],
    filePackagePath: string
  ): TypeInfo | null {
    if (!scopeNode) {
      return null;
    }

    // Record all temporary variables declared in this function
    const tempVariables: Map<string, TypeInfo> = new Map();

    // If scopeNode is a function definition, process its parameters
    if (scopeNode.type === 'function_definition') {
      const paramsNode = this.getChildByTypeForNode(scopeNode, 'parameters');
      if (paramsNode) {
        for (let i = 0; i < paramsNode.childCount; i++) {
          const param = paramsNode.child(i);
          if (param?.type === 'typed_parameter') {
            const paramName = param.child(0)?.text;
            const paramType = param.child(2);
            if (paramName && paramType) {
              // Check if type matches any imported modules
              for (const importedModule of importModules) {
                if (
                  paramType.text === importedModule.getModuleName() ||
                  paramType.text === importedModule.getAsName()
                ) {
                  tempVariables.set(paramName, {
                    packageName: importedModule.getFromPackage(),
                    typeName: importedModule.getModuleName(),
                  });
                  break;
                }
              }
            }
          }
        }
      }
    }

    // Find the path from scopeNode to contextNode
    const nodePath: Parser.SyntaxNode[] = [];
    let current: Parser.SyntaxNode | null = context.contextNode;

    while (current && current !== scopeNode /*!this.areNodesEqual(current, scopeNode)*/) {
      nodePath.unshift(current);
      current = current.parent;
    }

    // Add scopeNode if we found it
    if (current) {
      nodePath.unshift(current);
    }

    // Process each node in the path
    for (let i = 0; i <= nodePath.length - 1; i++) {
      const currentNode = nodePath[i];
      //const nextPathNode = nodePath[i + 1];

      // Process all siblings that come before the next path node
      const parent = currentNode;
      if (!parent) {
        continue;
      }

      for (let j = 0; j < parent.childCount; j++) {
        const sibling = parent.child(j);
        if (!sibling) {
          continue;
        }

        // Stop when we reach the next node in our path
        /*if (sibling === nextPathNode) {
          break;
        }*/
        if (this.areNodesEqual(sibling, context.contextNode)) {
          break;
        }

        // Process assignment statements
        if (sibling.type === 'expression_statement') {
          const expr = sibling.child(0);
          if (expr?.type === 'assignment' && expr.childCount === 3) {
            const left = expr.child(0);
            const right = expr.child(2);
            if (left && right) {
              const typeInfos = this.analyzeCallType(right, importModules, tempVariables, filePackagePath);

              // Handle tuple unpacking or single assignment
              if (left.type === 'pattern_list') {
                // Multiple identifiers on left side
                const identifiers = [];
                for (let k = 0; k < left.childCount; k++) {
                  const child = left.child(k);
                  if (child?.type === 'identifier') {
                    identifiers.push(child.text);
                  }
                }

                // Map each identifier to its corresponding TypeInfo
                identifiers.forEach((identifier, index) => {
                  if (index < typeInfos.length) {
                    tempVariables.set(identifier, typeInfos[index]);
                  }
                });
              } else {
                // Single identifier on left side
                if (typeInfos.length > 0) {
                  tempVariables.set(left.text, typeInfos[0]);
                }
              }
            }
          }
        }
      }
    }

    // Look up the identifier in our collected variables
    const directMatch = tempVariables.get(context.identifier);
    if (directMatch) {
      return directMatch;
    }

    // Handle subscript access (e.g., models['gpt-4'])
    if (context.subscriptBase && context.subscriptKey) {
      const baseObject = tempVariables.get(context.subscriptBase);
      if (baseObject) {
        // Query the object from index system
        const cacheObject = this.codeObjectIndexSystem.queryPythonCacheObject(
          filePackagePath,
          baseObject.packageName,
          baseObject.typeName
        );

        if (cacheObject?.subObjects) {
          // Find matching field in subObjects by name
          const field = cacheObject.subObjects.find(sub => sub.name === context.subscriptKey);
          if (field?.packagePath) {
            return {
              packageName: field.packagePath,
              typeName: field.name,
            };
          }
        }
      }
    }

    return null;
  }

  private analyzeCallType(
    callNode: Parser.SyntaxNode,
    importModules: PythonModule[],
    tempVariables: Map<string, TypeInfo>,
    filePackagePath: string
  ): TypeInfo[] {
    const functionNode = callNode.child(0);
    if (!functionNode) {
      return [];
    }

    // Case 1: class initialization (e.g., x = MyClass())
    if (functionNode.type === 'identifier') {
      const className = functionNode.text;
      // Try to match with imported modules
      for (const importedModule of importModules) {
        if (
          className === importedModule.getModuleName() ||
          className === importedModule.getAsName()
        ) {

          // 从缓存中查询是否存在，如果存在则则查看是什么类型，类型为Class的时候直接返回，
          // 为funtion的时候查看是否存在返回类型，存在则返回返回类型，不存在则返回类型本身
          const cacheObject = this.codeObjectIndexSystem.queryPythonCacheObject(
            filePackagePath,
            importedModule.getFromPackage(),
            importedModule.getModuleName()
          );

          // 如果存在, 类型为funtion的时候查看是否存在返回类型，存在则返回返回类型，不存在则返回类型本身
          // 如果是类类型，暂时不处理，跟随最后结果，直接返回
          // if (cacheObject.getObjectType() == 'class')
          if (cacheObject) {

            // 如果是函数类型，检查返回类型
            if (cacheObject.objectType == 'func') {
              // 从subObjects中查找返回类型
              const subObjects = cacheObject.subObjects;
              const returnType = subObjects?.find(obj => obj.type === 'return_type');
              if (returnType) {
                return [{
                  packageName: returnType.packagePath || '',
                  typeName: returnType.name,
                }];
              }
            }
          }

          return [{
            packageName: importedModule.getFromPackage(),
            typeName: importedModule.getModuleName(),
          }];
        }
      }
    }

    return [];
  }

  private analyzeAttributeType(attributeNode: Parser.SyntaxNode): TypeInfo[] | null {
    // Implementation would depend on your attribute type resolution logic
    return null;
  }

  private getRootNode(node: Parser.SyntaxNode): Parser.SyntaxNode {
    let current = node;
    while (current.parent) {
      current = current.parent;
    }
    return current;
  }

  private areNodesEqual(node1: Parser.SyntaxNode | null, node2: Parser.SyntaxNode | null): boolean {
    if (!node1 || !node2) {
      return false;
    }
    return (
      node1.type === node2.type &&
      node1.text === node2.text &&
      node1.startPosition.row === node2.startPosition.row &&
      node1.startPosition.column === node2.startPosition.column &&
      node1.endPosition.row === node2.endPosition.row &&
      node1.endPosition.column === node2.endPosition.column
    );
  }
}
