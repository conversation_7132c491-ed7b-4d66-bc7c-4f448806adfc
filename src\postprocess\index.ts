/**
 * 后处理模块
 * 提供一系列过滤器和处理函数，用于优化和清理补全结果
 */

import { AgentConfig } from "../AgentConfig";
import { CompletionContext, CompletionResponse } from "../CompletionContext";
import { isBrowser } from "../env";
import { logger } from "../logger";
import { applyFilter } from "./base";
import { calculateReplaceRangeByBracketStack } from "./calculateReplaceRangeByBracketStack";
import { calculateReplaceRangeBySyntax, supportedLanguages } from "./calculateReplaceRangeBySyntax";
import { dropBlank } from "./dropBlank";
import { dropDuplicated } from "./dropDuplicated";
import { dropUnfinishedMulti } from "./dropUnfinishedMulti";
import { formatIndentation } from "./formatIndentation";
import { limitScope } from "./limitScope";
import { processFillInTheLine } from "./processFillInTheLine";
import { removeDuplicatedBlockClosingLine } from "./removeDuplicatedBlockClosingLine";
import { removeLineEndsWithRepetition } from "./removeLineEndsWithRepetition";
import { removeRepetitiveBlocks } from "./removeRepetitiveBlocks";
import { removeRepetitiveLines } from "./removeRepetitiveLines";
import { trimSpace } from "./trimSpace";
// import { onnxPredictor, CompletionContext as OnnxCompletionContext } from "./OnnxPredictor";

/**
 * 使用ONNX模型过滤补全结果
 * 通过分析token概率预测补全质量，过滤掉低质量的补全
 *
 * @param response - 补全响应
 * @param context - 补全上下文
 * @param reqId - 请求ID，用于日志
 * @returns 过滤后的补全响应
 */
// export async function filterCompletionsByOnnx(
//   response: CompletionResponse,
//   context: CompletionContext,
//   reqId?: string
// ): Promise<CompletionResponse> {
//   const logPrefix = `[cpl-${reqId}]`;
//   const startTime = Date.now();

//   // 如果没有补全选项或token概率，直接返回原响应
//   if (!response.choices ||
//       response.choices.length === 0 ||
//       !response.token_probs ||
//       !Array.isArray(response.token_probs) ||
//       response.token_probs.length === 0) {
//     return response;
//   }

//   try {
//     logger.debug(`${logPrefix} Starting ONNX inference filtering`);

//     // 准备ONNX模型的上下文信息
//     const onnxContext: OnnxCompletionContext = {
//       prefix: context.prefixLines.join(""),
//       suffix: context.suffixLines.join(""),
//       language: context.language,
//       is_single_line: !!context.singleLine,
//       snippets: []
//     };

//     // 直接使用OnnxPredictor中的shouldAcceptCompletion方法
//     const predictResult = await onnxPredictor.shouldAcceptCompletion(response.token_probs, onnxContext);
//     const endTime = Date.now();

//     logger.debug(`${logPrefix} ONNX inference completed in ${endTime - startTime}ms, prediction: ${predictResult.prediction}, probabilities: [${predictResult.probabilities.map(p => p.toFixed(6)).join(', ')}]`);

//     // 使用shouldAcceptCompletion的预测结果决定是否过滤
//     if (!predictResult.prediction) {
//       logger.debug(`${logPrefix} ONNX model rejected completion`);
//       // 清空补全结果
//       response.choices = [];
//     } else {
//       logger.debug(`${logPrefix} ONNX model accepted completion`);
//     }

//     // 清除token_probs以避免重复处理
//     delete response.token_probs;

//     return response;
//   } catch (error) {
//     // ONNX推理错误，记录错误并返回原响应
//     logger.error(`${logPrefix} ONNX inference error: ${error}`);
//     return response;
//   }
// }

/**
 * 预缓存处理
 * 在将补全结果写入缓存前进行基本清理和格式化
 * 仅在缓存未命中时调用
 *
 * @param context - 补全上下文
 * @param config - 后处理配置
 * @param response - 补全响应
 * @param reqId - 请求ID（用于日志）
 * @returns 处理后的补全响应
 */
export async function preCacheProcess(
  context: CompletionContext,
  _: AgentConfig["postprocess"],
  response: CompletionResponse,
  reqId?: string,

): Promise<CompletionResponse> {
  const logPrefix = `[cpl-${reqId}]`;
  const startTime = Date.now();
  let currentTime = startTime;

  logger.debug(`${logPrefix} Starting pre-cache processing`);

  // // 首先使用ONNX过滤 - 这不能使用applyFilter模式，因为需要访问整个response对象
  // if (response.token_probs && response.token_probs.length > 0) {
  //   const onnxFilterStartTime = Date.now();

  //   response = await filterCompletionsByOnnx(response, context, reqId);

  //   const onnxFilterEndTime = Date.now();
  //   logger.debug(`${logPrefix} ONNX filtering completed in ${onnxFilterEndTime - onnxFilterStartTime}ms`);
  //   currentTime = onnxFilterEndTime;

  //   if (response.choices.length === 0) {
  //     logger.debug(`${logPrefix} Pre-cache processing early exit: ONNX filtered all completions`);
  //     return response;
  //   }
  // }

  return Promise.resolve(response)
    .then(applyFilter(removeLineEndsWithRepetition(), context))
    .then((result) => {
      const newTime = Date.now();
      logger.debug(`${logPrefix} removeLineEndsWithRepetition completed in ${newTime - currentTime}ms`);
      currentTime = newTime;
      return result;
    })
    .then(applyFilter(dropUnfinishedMulti(), context))
    .then((result) => {
      const newTime = Date.now();
      logger.debug(`${logPrefix} dropUnfinishedMulti completed in ${newTime - currentTime}ms`);
      currentTime = newTime;
      return result;
    })
    .then(applyFilter(dropDuplicated(), context))
    .then((result) => {
      const newTime = Date.now();
      logger.debug(`${logPrefix} dropDuplicated completed in ${newTime - currentTime}ms`);
      currentTime = newTime;
      return result;
    })
    .then(applyFilter(trimSpace(), context))
    .then((result) => {
      const newTime = Date.now();
      logger.debug(`${logPrefix} trimSpace completed in ${newTime - currentTime}ms`);
      currentTime = newTime;
      return result;
    })
    .then(applyFilter(dropBlank(), context))
    .then((result) => {
      const newTime = Date.now();
      logger.debug(`${logPrefix} dropBlank completed in ${newTime - currentTime}ms`);
      currentTime = newTime;
      return result;
    })
    .then((result) => {
      const endTime = Date.now();
      logger.debug(`${logPrefix} Pre-cache processing completed in ${endTime - startTime}ms`);
      return result;
    });
}

/**
 * 后缓存处理
 * 对补全结果进行完整的后处理，包括格式化、去重和清理
 * 无论是否命中缓存都会调用
 *
 * @param context - 补全上下文
 * @param config - 后处理配置
 * @param response - 补全响应
 * @param reqId - 请求ID（用于日志）
 * @returns 处理后的补全响应
 */
export async function postCacheProcess(
  context: CompletionContext,
  config: AgentConfig["postprocess"],
  response: CompletionResponse,
  reqId?: string,

): Promise<CompletionResponse> {
  const logPrefix = `[cpl-${reqId}]`;
  const startTime = Date.now();
  let currentTime = startTime;

  logger.debug(`${logPrefix} Starting post-cache processing`);

  // 首先使用ONNX过滤 - 这不能使用applyFilter模式，因为需要访问整个response对象
  // if (response.token_probs && response.token_probs.length > 0) {
  //   const onnxFilterStartTime = Date.now();

  //   response = await filterCompletionsByOnnx(response, context, reqId);

  //   const onnxFilterEndTime = Date.now();
  //   logger.debug(`${logPrefix} ONNX filtering completed in ${onnxFilterEndTime - onnxFilterStartTime}ms`);
  //   currentTime = onnxFilterEndTime;

  //   if (response.choices.length === 0) {
  //     logger.debug(`${logPrefix} Post-cache processing early exit: ONNX filtered all completions`);
  //     return response;
  //   }
  // }

  return Promise.resolve(response)
    .then(applyFilter(dropUnfinishedMulti(), context))
    .then((result) => {
      const newTime = Date.now();
      logger.debug(`${logPrefix} dropUnfinishedMulti completed in ${newTime - currentTime}ms`);
      currentTime = newTime;
      return result;
    })
    .then(applyFilter(removeRepetitiveBlocks(), context))
    .then((result) => {
      const newTime = Date.now();
      logger.debug(`${logPrefix} removeRepetitiveBlocks completed in ${newTime - currentTime}ms`);
      currentTime = newTime;
      return result;
    })
    .then(applyFilter(removeRepetitiveLines(), context))
    .then((result) => {
      const newTime = Date.now();
      logger.debug(`${logPrefix} removeRepetitiveLines completed in ${newTime - currentTime}ms`);
      currentTime = newTime;
      return result;
    })
    .then(applyFilter(limitScope(config["limitScope"]), context))
    .then((result) => {
      const newTime = Date.now();
      logger.debug(`${logPrefix} limitScope completed in ${newTime - currentTime}ms`);
      currentTime = newTime;
      return result;
    })
    .then(applyFilter(removeDuplicatedBlockClosingLine(), context))
    .then((result) => {
      const newTime = Date.now();
      logger.debug(`${logPrefix} removeDuplicatedBlockClosingLine completed in ${newTime - currentTime}ms`);
      currentTime = newTime;
      return result;
    })
    .then(applyFilter(formatIndentation(), context))
    .then((result) => {
      const newTime = Date.now();
      logger.debug(`${logPrefix} formatIndentation completed in ${newTime - currentTime}ms`);
      currentTime = newTime;
      return result;
    })
    .then(applyFilter(dropDuplicated(), context))
    .then((result) => {
      const newTime = Date.now();
      logger.debug(`${logPrefix} dropDuplicated completed in ${newTime - currentTime}ms`);
      currentTime = newTime;
      return result;
    })
    .then(applyFilter(trimSpace(), context))
    .then((result) => {
      const newTime = Date.now();
      logger.debug(`${logPrefix} trimSpace completed in ${newTime - currentTime}ms`);
      currentTime = newTime;
      return result;
    })
    .then(applyFilter(dropBlank(), context))
    .then((result) => {
      const newTime = Date.now();
      logger.debug(`${logPrefix} dropBlank completed in ${newTime - currentTime}ms`);
      currentTime = newTime;
      return result;
    })
    .then(applyFilter(processFillInTheLine(), context))
    .then((result) => {
      const newTime = Date.now();
      logger.debug(`${logPrefix} processFillInTheLine completed in ${newTime - currentTime}ms`);
      currentTime = newTime;
      return result;
    })
    .then((result) => {
      const endTime = Date.now();
      logger.debug(`${logPrefix} Post-cache processing completed in ${endTime - startTime}ms`);
      return result;
    });
}

/**
 * 计算替换范围
 * 根据环境和配置选择合适的替换范围计算方法
 *
 * @param context - 补全上下文
 * @param config - 后处理配置
 * @param response - 补全响应
 * @returns 处理后的补全响应
 */
export async function calculateReplaceRange(
  context: CompletionContext,
  config: AgentConfig["postprocess"],
  response: CompletionResponse,
): Promise<CompletionResponse> {
  return isBrowser || // 浏览器环境暂不支持语法解析器
    !config["calculateReplaceRange"].experimentalSyntax ||
    !supportedLanguages.includes(context.language)
    ? calculateReplaceRangeByBracketStack(response, context)
    : calculateReplaceRangeBySyntax(response, context);
}

export * from './OnnxPredictor';
