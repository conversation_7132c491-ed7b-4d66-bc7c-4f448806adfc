import CodeObjectIndexSystem from './codeObjectIndexSystem';
import RelativeCodeObject from './relativeCodeObject';
import { IRelativeCodeFinder, ParserConstructor } from './types';


// todo lys 处理常量
// import { LRU_CACHE_SIZE } from '../../common/config';
import * as fs from 'fs/promises';
import * as path from 'path';
import { UpdatedFile } from "../CompletionContext";
import { SupportedLanguages, inferLanguage, isSupportedLanguage } from '../languages';
import { logger } from '../logger';
import { GoParser } from './go/goParser';
import { GO_MODULE_DEF_FILENAME, GoUtils } from './go/goUtils';
import { JavaParser } from './java/javaParser';
import { JavaScriptParser } from './javaScript/javaScriptParser';
import { PythonParser } from './python/pythonParser';

export default class ProjectCodeIndexer {
  // 单例模式
  private static instance: ProjectCodeIndexer | null;

  /**
   * 用于跟踪索引构建过程的Promise。
   * 可用于等待初始化扫描完成。
   * @public
   */
  public initializationPromise: Promise<void> | null = null;
  public isReady = false;

  // 使用LRU算法储存最近找到的代码关联对象 报废  todo lys 后续删除
  // private foundRelativeCodes: FoundRelativeCodeLRU<string, RelativeCodeObject>;

  // 全局代码对象索引系统类
  // 在插件启动时，将整个代码工程主要代码文件解释，由各语言的ParseFile方法将每个代码文件进行语法树解释后，将主要的代码对象储存在这里
  // 同时，代码工程本身的文件变更也会影响本索引系统，因此增加文件删除、文件更名、包路径更新等操作，以维护索引的正确性
  // 在每次代码补全时，各代码语言的CodeFinder类将会从本索引系统中查询需要关联的代码对象
  private codeObjectIndexSystem: CodeObjectIndexSystem;

  // 代码语言处理集合类, 保存所有支持的语言的处理类
  // 每个代码语言都需要有一个独立的代码语言处理类，解析文件、查找相关代码对象等工作都在该类中进行
  private codeFinders: Map<string, IRelativeCodeFinder>;

  private parserRegistry: Map<string, ParserConstructor<any>> = new Map();

  // tabby migration: projectPath and storagePath can be passed here if available in the new environment
  private projectPath?: string;

  private constructor(projectPath?: string) {
    this.projectPath = projectPath;
    this.codeObjectIndexSystem = new CodeObjectIndexSystem();
    // this.foundRelativeCodes = new FoundRelativeCodeLRU<string, RelativeCodeObject>(4);
    this.codeFinders = new Map<string, IRelativeCodeFinder>();

    this.initParserRegistry();
    this.initializationPromise = this.buildIndexForProject().then(() => {
      this.isReady = true;
      logger.info(`[ProjectCodeIndexer] Indexing completed and is now ready.`);
    });
  }

  private initParserRegistry() {
    this.parserRegistry.set(SupportedLanguages.JAVA, JavaParser);
    this.parserRegistry.set(SupportedLanguages.GO, GoParser);
    this.parserRegistry.set(SupportedLanguages.JAVASCRIPT, JavaScriptParser);
    this.parserRegistry.set(SupportedLanguages.PYTHON, PythonParser);
  }

  // tabby migration: context: vscode.ExtensionContext removed
  // public async registerCodeFinder(codeType: string, context: vscode.ExtensionContext) {
  // 修改为传入projectPath,该函数是为了防止初始化的时候，没有codetype对应的语言，导致缺少codefinder
  public async registerCodeFinder(codeType: string, projectPath?: string) {
    if (this.parserRegistry.has(codeType)) {
      if (this.codeFinders.has(codeType)) {
        return;
      }
      const parser = this.parserRegistry.get(codeType)!;
      const codeFinder = new parser(this.codeObjectIndexSystem);
      this.codeFinders.set(codeType, codeFinder);
      // tabby migration: pass projectPath and storagePath to initialize
      await codeFinder.initialize(projectPath);
    }
  }

  /**
   * 获取ProjectCodeIndexer的单例实例。
   * 如果实例已存在，则直接返回。
   * @returns {ProjectCodeIndexer} ProjectCodeIndexer的实例。
   * @throws {Error} 如果实例尚未初始化，则抛出错误。
   */
  public static getInstance(): ProjectCodeIndexer;
  /**
   * 获取或初始化ProjectCodeIndexer的单例实例。
   * 这是首次创建实例时必须调用的方法。
   * @param {string} projectPath - 工作区的根路径。
   * @returns {ProjectCodeIndexer} ProjectCodeIndexer的实例。
   */
  public static getInstance(projectPath: string): ProjectCodeIndexer;
  public static getInstance(projectPath?: string): ProjectCodeIndexer {
    if (ProjectCodeIndexer.instance == null) {
      if (typeof projectPath !== 'string') {
        throw new Error("[cf tabby]ProjectCodeIndexer has not been initialized. Call getInstance with projectPath first.");
      }
      ProjectCodeIndexer.instance = new ProjectCodeIndexer(projectPath);
    }
    return ProjectCodeIndexer.instance;
  }

  // tabby migration: context: vscode.ExtensionContext removed. Passes stored projectPath and storagePath.
  // public async initialize(context: vscode.ExtensionContext): Promise<void> {
  // todo lys 干嘛用的？
  // public async initialize(projectPath?: string): Promise<void> {
  //   // Store paths for later use if provided
  //   this.projectPath = projectPath;

  //   const initPromises: Promise<void>[] = [];
  //   for (const codeFinder of this.codeFinders.values()) {
  //     // tabby migration: pass stored projectPath and storagePath to initialize
  //     initPromises.push(codeFinder.initialize(this.projectPath));
  //   }
  //   await Promise.all(initPromises);
  // }

  public buildGoModMapping(moduleName: string, moduleDirPath: string) {
    this.codeObjectIndexSystem.updateGoModMap(moduleName, moduleDirPath);
  }

  // 解释代码文件，将代码文件中的主要语法对象解释出来，并存入代码对象缓存索引系统，以提供后续跨文件对象关联使用
  // 一般在插件启动的时候遍历工程代码文件并对每个文件调用本方法，或者有文件发生非用户输入更新时调用本方法
  public async ParseFile(codeTypeName: string, fileName: string, codeFileContent: string): Promise<void> {
    // this.foundRelativeCodes.remove(fileName);
    if (this.codeFinders.has(codeTypeName)) {

      try {
        await this.codeFinders.get(codeTypeName)!.ParseFile(fileName, codeFileContent);
      } catch {
        logger.error("[cf tabby]ParseFile error:" + fileName + " : " + codeTypeName);
      }
    }
  }

  // 查找当前光标位置处所代码相关联的对象信息。主要为了代码补全跨文件对象关联。在用户更新代码编辑器代码时触发本方法调用
  // 当用户在输入代码，符合触发代码补全条件时，调用本接口
  // tabby migration: document: TextDocument replaced with fileContent, filePath, languageId
  public async FindRelativeObject(
    fileContent: string,
    filePath: string,
    line: number,
    column: number
  ): Promise<RelativeCodeObject[] | null> {
    if (!this.isReady) {
      logger.debug("[cf tabby] ProjectCodeIndexer Index is not ready, skipping FindRelativeObject.");
      return null;
    }

    const language = inferLanguage(filePath);

    // 通过文件路径读取文件内容        出现缓存问题，改为直接从vsc获取 todo lys
    // let fileContent: string;
    try {
      // fileContent = await fs.readFile(filePath, 'utf-8');
      logger.debug(`[cf tabby] ProjectCodeIndexer Reading file: ${filePath}, language: ${language}, line: ${line}, column: ${column}`);
      // logger.debug(`[cf tabby] ProjectCodeIndexer File content: ${fileContent}`);
    } catch (error) {
      logger.error(`[cf tabby] ProjectCodeIndexer Failed to read file: ${filePath}`, error);
      return null;
    }

    if (this.codeFinders.has(language)) {
      const rco = this.codeFinders.get(language)!.FindRelativeObject(fileContent, filePath, language, line, column);
      if (rco) {
        // logger.info(`[RLCC] Found relative objects for completion:`, rco);
        return [rco];
      }
    }
    return null;
  }

  // 更新文件索引
  public async updateFiles(files: UpdatedFile[]): Promise<void> {
    if (!this.isReady) {
      logger.debug('[cf tabby] ProjectCodeIndexer Index is not ready, skipping updateFiles.');
      return;
    }
    logger.debug(`[cf tabby] ProjectCodeIndexer Processing file updates: ${JSON.stringify(files)}`);

    // 遍历文件列表
    for (const file of files) {

      if (file.updateType === "delete") {
        // 删除文件索引
        if (file.isDirectory) {
          // 删除目录下的所有文件
          this.deleteByDirectory(file.filePath);
        } else {
          // 删除单个文件索引
          this.deleteFileIndex(file.filePath);
        }
        logger.debug(
          `[cf tabby]ProjectCodeIndexer Deleted index for: ${file.filePath}`
        );

      } else if (file.updateType === "change" || file.updateType === "create") {
        // 更新文件索引
        if (file.isDirectory) {
          // 更新目录下的所有文件
          await this.createOrUpdateDirectoryIndex(file.filePath);
        } else {
          // 更新单个文件索引
          await this.createOrUpdateFileIndex(file.filePath);
        }
        logger.debug(
          `[cf tabby]ProjectCodeIndexer Created/Updated index for: ${file.filePath}`
        );
      // todo lys 先注释，不处理update的脏读问题
      // }else if(file.updateType === "change") {
      //   // 只有在文件为"go.mod"时，才需要处理  todo lys
      //   // 后续把handleGoMod里面的文件内容获取改为从外面传入
      //   // 操作系统的文件缓存：为了提高性能，现代操作系统（Windows, macOS, Linux）都会将频繁读写的文件内容缓存在内存中（这被称为 Page Cache 或 File System Cache）。
      //   // 当您调用 fs.readFile 时，操作系统会首先检查文件内容是否在内存缓存中。如果您在这极短的延迟窗口内调用 fs.readFile，操作系统可能会从它的（尚未更新的）缓存中返回旧的数据给您。
      //   // 您越是频繁、快速地连续读取，就越有可能碰到这种情况。所以不能直接用fs.readFile来获取文件内容
      //   const fileName = path.basename(file.filePath);
      //   if (GoUtils.equals(fileName, GO_MODULE_DEF_FILENAME)) {
      //     await GoUtils.handleGoMod(file.filePath);
      //   }
      }

    }
  }


  // 删除文件
  public deleteFileIndex(fileName: string): void {
    if (!this.isReady) {
      logger.debug("[cf tabby]ProjectCodeIndexer Index is not ready, skipping deleteFileIndex.");
      return;
    }
    // this.foundRelativeCodes.remove(fileName);  lru缓存
    this.codeObjectIndexSystem.deleteByFileName(fileName);
  }


  // 删除目录下的所有文件
  public deleteByDirectory(folderName: string): void {
    if (!this.isReady) {
      logger.debug("[cf tabby]ProjectCodeIndexer Index is not ready, skipping DeleteByDirectory.");
      return;
    }
    // this.foundRelativeCodes.removeFolder(folderName);  lru缓存
    this.codeObjectIndexSystem.deleteByDirectory(folderName);
  }


  //新增或者更新文件索引
  public async createOrUpdateFileIndex(filePath: string): Promise<void> {
    if (!this.isReady) {
      logger.debug("[cf tabby]ProjectCodeIndexer Index is not ready, skipping createOrUpdateFileIndex.");
      return;
    }

    logger.debug(`[cf tabby]ProjectCodeIndexer createOrUpdateFileIndex event for: ${filePath}`);
    // RunCompletionCache.clearCache();

    // 处理go.mod文件
    const fileName = path.basename(filePath);
    if (GoUtils.equals(fileName, GO_MODULE_DEF_FILENAME)) {
      await GoUtils.handleGoMod(filePath);
      return;
    }

    // 处理其他文件
    const languageId = inferLanguage(filePath);
    if (isSupportedLanguage(languageId)) {
      try {
        const content = await fs.readFile(filePath, 'utf-8');
        await this.registerCodeFinder(languageId, this.projectPath);
        await this.ParseFile(languageId, filePath, content);
        logger.info(`[cf tabby]ProjectCodeIndexer createOrUpdateFileIndex: ${filePath}. Called projCodeIndexer.handleFileChange.`);
      } catch (err) {
        logger.error(`[cf tabby]ProjectCodeIndexer Error update file index ${filePath}: ${err}`);
      }
    }
  }


  //新增或者更新目录下的所有文件索引
  public async createOrUpdateDirectoryIndex(directoryPath: string): Promise<void> {
    if (!this.isReady) {
      logger.debug("[cf tabby]ProjectCodeIndexer Index is not ready, skipping createOrUpdateDirectoryIndex.");
      return;
    }

    logger.debug(`[cf tabby]ProjectCodeIndexer createOrUpdateDirectoryIndex event for: ${directoryPath}`);

    try {
      const files = await fs.readdir(directoryPath);
      for (const file of files) {
        const filePath = path.join(directoryPath, file);
        const stat = await fs.stat(filePath);

        if (stat.isDirectory()) {
          await this.createOrUpdateDirectoryIndex(filePath);
        } else if (stat.isFile()) {
          await this.createOrUpdateFileIndex(filePath);
        }
      }
    } catch (err) {
      logger.error(`[cf tabby]ProjectCodeIndexer Error updating directory index ${directoryPath}: ${err}`);
    }
  }

  public async buildIndexForProject() {
    if (!this.projectPath) {
      logger.warn(`[cf tabby]ProjectCodeIndexer Cannot build index, projectPath is not set.`);
      return;
    }
    logger.info(`[cf tabby]ProjectCodeIndexer Starting initial scan of project: ${this.projectPath}`);
    const excludeFolders = ['.git', 'node_modules'];

    const readGoModFileRecursively = async (dirPath: string) => {
      try {
        const entries = await fs.readdir(dirPath, { withFileTypes: true });
        for (const entry of entries) {
          const fullPath = path.join(dirPath, entry.name);
          if (entry.isDirectory()) {
            if (!excludeFolders.includes(entry.name.toLocaleLowerCase())) {
              await readGoModFileRecursively(fullPath);
            }
          } else if (entry.isFile()) {
            if (GoUtils.equals(entry.name, GO_MODULE_DEF_FILENAME)) {
              await GoUtils.handleGoMod(fullPath);
            }
          }
        }
      } catch (error) {
        logger.warn(`[cf tabby]ProjectCodeIndexer Error reading directory ${dirPath} for go.mod scan: ${error}`);
      }
    };

    const readFilesRecursively = async (dirPath: string) => {
      try {
        const entries = await fs.readdir(dirPath, { withFileTypes: true });
        for (const entry of entries) {
          const fullPath = path.join(dirPath, entry.name);
          if (entry.isDirectory()) {
            if (!excludeFolders.includes(entry.name.toLocaleLowerCase())) {
              await readFilesRecursively(fullPath);
            }
          } else if (entry.isFile()) {
            const languageId = inferLanguage(fullPath);
            if (isSupportedLanguage(languageId)) {
              await this.registerCodeFinder(languageId, this.projectPath);
              try {
                const content = await fs.readFile(fullPath, 'utf-8');
                await this.ParseFile(languageId, fullPath, content);
              } catch (err) {
                logger.error(`[cf tabby]ProjectCodeIndexer Error reading file ${fullPath} during initial scan: ${err}`);
              }
            }
          }
        }
      } catch (error) {
        logger.warn(`[cf tabby]ProjectCodeIndexer Error reading directory ${dirPath} for indexing: ${error}`);
      }
    };

    await readGoModFileRecursively(this.projectPath);
    await readFilesRecursively(this.projectPath);
    logger.info(`[cf tabby]ProjectCodeIndexer Initial scan completed for project: ${this.projectPath}`);
  }


}
