// import { getOnnxBindingPath } from '../utils/onnx-resolver';

// eslint-disable-next-line @typescript-eslint/no-var-requires
// const onnx = require(getOnnxBindingPath());

/**
 * TokenProb 接口 - 表示单个令牌及其概率
 * 对应Rust中的TokenProb结构体
 */
export interface TokenProb {
  text: string;
  prob: number;  // 在原始JSON中这是probability字段
  candidates?: [string, number][];  // 在原始JSON中包含候选项
}

/**
 * 补全上下文接口 - 提供代码补全的上下文信息
 * 对应Rust中的AdditionalInfo结构体
 */
export interface CompletionContext {
  prefix: string;
  suffix: string;
  is_single_line: boolean;
  language: string;
  snippets: string[];  // 对应Rust中的bm25_results
}

/**
 * 预测响应接口 - 表示模型预测的结果
 * 对应Rust中的PredictResponse结构体
 */
export interface PredictResponse {
  prediction: number;  // 对应Rust中的i64
  probabilities: number[];  // 对应Rust中的Vec<f32>
}

/**
 * ONNX模型预测器类
 * 使用ONNX运行时执行模型推理
 * 对应Rust中的OnnxModel结构体及其实现
 */
// export class OnnxPredictor {
//   private session: any = null; // 使用any类型代替onnx.InferenceSession
//   private static LOG_PREFIX = '[onnx-predictor]';
//   private modelPath: string = path.join(__dirname, 'cpl-rf-predictor.onnx'); // 默认路径

//   /**
//    * 构造函数 - 初始化ONNX预测器
//    * @param modelPath 模型文件路径，默认为相对路径
//    */
//   constructor(modelPath?: string) {
//     console.error("OnnxPredictor constructor started");
//     // 如果提供了路径，使用提供的路径
//     if (modelPath) {
//       this.modelPath = modelPath;
//       return;
//     }
//     console.error("OnnxPredictor constructor 2");

//     // 否则，尝试多个可能的路径位置
//     const possiblePaths = [
//       // 开发环境路径
//       path.join(__dirname, 'cpl-rf-predictor.onnx'),
//       // 生产环境路径 (dist)
//       path.join(__dirname, '..', '..', 'dist',  'cpl-rf-predictor.onnx'),
//       // 当直接从dist运行时的路径
//       path.join(__dirname, 'postprocess', 'cpl-rf-predictor.onnx'),
//       // 源代码目录
//       path.join(__dirname, '..', '..', 'src', 'postprocess', 'cpl-rf-predictor.onnx')
//     ];
//     console.error("OnnxPredictor constructor 3");
//     // 查找第一个存在的路径
//     for (const tryPath of possiblePaths) {
//       if (fs.existsSync(tryPath)) {
//         this.modelPath = tryPath;
//         break;
//       }
//     }
//     console.error("OnnxPredictor constructor 4");
//     logger.debug(`${OnnxPredictor.LOG_PREFIX} Model path: ${this.modelPath}`);
//   }

//   /**
//    * 初始化ONNX会话
//    * 对应Rust中的OnnxModel::new函数
//    */
//   public async initialize(): Promise<boolean> {
//     try {
//       // 检查模型文件是否存在
//       if (!fs.existsSync(this.modelPath)) {
//         logger.error(`${OnnxPredictor.LOG_PREFIX} Model file not found at ${this.modelPath}`);
//         return false;
//       }

//       // 创建ONNX会话
//       const options: any = { // 使用any类型代替onnx.InferenceSession.SessionOptions
//         executionProviders: ['cpu'],
//         graphOptimizationLevel: 'all',
//         logSeverityLevel: 1,
//         logVerbosityLevel: 0,
//         intraOpNumThreads: 1
//       };

//       logger.debug(`${OnnxPredictor.LOG_PREFIX} Creating ONNX session...`);
//       this.session = await onnx.InferenceSession.create(this.modelPath, options);

//       // 打印模型信息
//       logger.debug(`${OnnxPredictor.LOG_PREFIX} ONNX session created successfully`);

//       return true;
//     } catch (error) {
//       logger.error(`${OnnxPredictor.LOG_PREFIX} Failed to initialize ONNX session: ${error}`);
//       return false;
//     }
//   }

//   /**
//    * 从token概率数据提取特征
//    * 对应Rust中的extract_features函数
//    * @param tokenProbs 词元概率数组
//    * @param context 可选的补全上下文
//    * @returns 提取的特征矩阵
//    */
//   private extractFeatures(tokenProbs: TokenProb[], context?: CompletionContext): number[][] {
//     const startTime = Date.now();
//     logger.debug(`${OnnxPredictor.LOG_PREFIX} Starting feature extraction`);

//     // 提取概率值
//     const probs = tokenProbs.map(t => t.prob);

//     // 处理空概率数组
//     if (probs.length === 0) {
//       probs.push(0.0);
//     }

//     // 基本统计特征
//     const mean = probs.reduce((sum, p) => sum + p, 0) / probs.length;
//     const min = Math.min(...probs);
//     const max = Math.max(...probs);

//     // 计算标准差
//     let stdDev = 0.0;
//     if (probs.length > 1) {
//       const variance = probs.reduce((sum, p) => sum + Math.pow(p - mean, 2), 0) / probs.length;
//       stdDev = Math.sqrt(variance);
//     }

//     // 计算Q1(25%分位数)
//     const sortedProbs = [...probs].sort((a, b) => a - b);
//     let q1 = min;
//     if (probs.length > 1) {
//       const q1Idx = Math.floor(probs.length * 0.25);
//       q1 = sortedProbs[q1Idx] || min;
//     }

//     // 计算序列特征
//     const probDiffs: number[] = [];
//     for (let i = 1; i < probs.length; i++) {
//       const current = probs[i] || 0;
//       const previous = probs[i-1] || 0;
//       probDiffs.push(current - previous);
//     }

//     // 最大下降幅度
//     const maxDrop = probDiffs.length > 0 ?
//       Math.abs(probDiffs.reduce((min, d) => Math.min(min, d), 0)) : 0.0;

//     // 波动性
//     let volatility = 0.0;
//     if (probDiffs.length > 0) {
//       const diffMean = probDiffs.reduce((sum, d) => sum + d, 0) / probDiffs.length;
//       const variance = probDiffs.reduce((sum, d) => sum + Math.pow(d - diffMean, 2), 0) / probDiffs.length;
//       volatility = Math.sqrt(variance);
//     }

//     // 结束质量
//     const endQuality = probs.length >= 3 ?
//       probs.slice(-3).reduce((sum, p) => sum + p, 0) / 3.0 :
//       mean;

//     // 上下文特征
//     const prefixLen = context ? context.prefix.length : 0;
//     const suffixLen = context ? context.suffix.length : 0;

//     const log1pPrefixLen = Math.log(prefixLen + 1);
//     const log1pSuffixLen = Math.log(suffixLen + 1);
//     const contextRatio = (prefixLen + suffixLen) > 0 ?
//       prefixLen / (prefixLen + suffixLen) : 0.0;

//     // 创建特征数组
//     const features = [
//       mean,
//       min,
//       max,
//       stdDev,
//       q1,
//       probs.length,
//       probs[0] || 0,
//       probs[probs.length - 1] || 0,
//       maxDrop,
//       volatility,
//       endQuality,
//       log1pPrefixLen,
//       log1pSuffixLen,
//       contextRatio
//     ];

//     logger.debug(`${OnnxPredictor.LOG_PREFIX} Feature extraction completed in ${Date.now() - startTime}ms`);
//     return [features];
//   }

//   /**
//    * 执行模型预测
//    * 对应Rust中的OnnxModel::predict函数
//    * @param features 特征矩阵
//    * @returns 预测结果
//    */
//   private async predict(features: number[][]): Promise<PredictResponse> {
//     if (!this.session) {
//       throw new Error('ONNX session not initialized');
//     }

//     const startTime = Date.now();
//     logger.debug(`${OnnxPredictor.LOG_PREFIX} Starting model prediction`);

//     try {
//       // 创建输入tensor
//       const featuresFlat = features.flat();
//       const inputTensor = new onnx.Tensor('float32', featuresFlat, [features.length, featuresFlat.length / features.length]);
//       const feeds = { float_input: inputTensor };

//       // 运行模型推理
//       const results = await this.session.run(feeds);

//       // 提取预测结果
//       // 注意：根据Rust代码，我们期望有两个输出:
//       // 1. 类别预测 - i64类型的标量
//       // 2. 概率数组 - float32类型的数组
//       const classValue = results['label']?.data[0] as number || 1;
//       const probValues = Array.from(results['probabilities']?.data as Float32Array || [0, 1]);

//       const response: PredictResponse = {
//         prediction: classValue,
//         probabilities: probValues
//       };

//       logger.debug(`${OnnxPredictor.LOG_PREFIX} Model prediction completed in ${Date.now() - startTime}ms`);
//       return response;
//     } catch (error) {
//       logger.error(`${OnnxPredictor.LOG_PREFIX} Prediction error: ${error}`);
//       throw error;
//     }
//   }

//   /**
//    * 预测是否应该接受补全结果
//    * 主要公共接口方法
//    * @param tokenProbs 词元概率数组
//    * @param context 可选的补全上下文
//    * @returns 是否应该接受补全的预测结果
//    */
//   public async shouldAcceptCompletion(tokenProbs: TokenProb[], context?: CompletionContext): Promise<PredictResponse> {
//     try {
//       // 如果词元概率为空，返回默认接受
//       if (!tokenProbs || tokenProbs.length === 0) {
//         logger.debug(`${OnnxPredictor.LOG_PREFIX} No token probabilities available, skipping ONNX filtering`);
//         return {
//           prediction: 1,
//           probabilities: [0, 1]
//         };
//       }

//       // 如果会话未初始化，尝试初始化
//       if (!this.session) {
//         const initialized = await this.initialize();
//         if (!initialized) {
//           throw new Error('Failed to initialize ONNX session');
//         }
//       }

//       const startTime = Date.now();

//       // 提取特征并进行预测
//       const features = this.extractFeatures(tokenProbs, context);
//       const result = await this.predict(features);

//       const endTime = Date.now();
//       logger.debug(`${OnnxPredictor.LOG_PREFIX} Prediction completed in ${endTime - startTime}ms`);

//       // 参考 Rust 代码的阈值判断
//       const passThreshold = 0.65;
//       const probabilities = result.probabilities || [0, 1];
//       const passedProb = probabilities[1] || 0;

//       if (passedProb < passThreshold) {
//         logger.debug(`${OnnxPredictor.LOG_PREFIX} Prediction suggests filtering (passed probability: ${passedProb.toFixed(6)} < threshold: ${passThreshold.toFixed(6)})`);
//         result.prediction = 0; // 确保使用一致的预测值
//       } else {
//         logger.debug(`${OnnxPredictor.LOG_PREFIX} Prediction suggests accepting (passed probability: ${passedProb.toFixed(6)} >= threshold: ${passThreshold.toFixed(6)})`);
//         result.prediction = 1; // 确保使用一致的预测值
//       }

//       return result;
//     } catch (error) {
//       logger.error(`${OnnxPredictor.LOG_PREFIX} Failed to predict completion acceptance: ${error}`);
//       // 默认返回接受补全(prediction: 1)
//       return {
//         prediction: 1,
//         probabilities: [0, 1]
//       };
//     }
//   }
// }

// 创建一个单例实例供全局使用
// export const onnxPredictor = new OnnxPredictor();
