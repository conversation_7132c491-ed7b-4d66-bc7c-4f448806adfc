import Parser = require("web-tree-sitter");
// import Parser from 'web-tree-sitter';
import { logger } from "../../logger";
import { getParser } from "../../syntax/parser";
import CodeObjectIndexSystem from "../codeObjectIndexSystem";
import RelativeCodeObject from "../relativeCodeObject";
import { IRelativeCodeFinder } from "../types";
import { UpdateObject } from "../updateObject";

type Node = Parser.SyntaxNode;
interface DescendantNode {
  packageSuffix: string;
  node: Node;
  nodeIdentifierName: string;
}

interface VariableDeclaration {
  packageName: string;
  typeName: string;
}

interface Variables {
  [name: string]: VariableDeclaration;
}

export class JavaParser implements IRelativeCodeFinder {
  private parser: Parser | null;

  private objectSystem: CodeObjectIndexSystem;

  private initializationPromise: Promise<void> | null = null;

  public constructor(codeObjectIndexSystem: CodeObjectIndexSystem) {
    this.parser = null;
    this.objectSystem = codeObjectIndexSystem;
  }

  // tabby migration: context: vscode.ExtensionContext replaced with workspaceRootPath, storagePath
  // public initialize(context: vscode.ExtensionContext): Promise<void> {
  public initialize(workspaceRootPath?: string): Promise<void> {
    if (!this.initializationPromise) {
      // tabby migration: context.extensionUri is no longer available, assuming wasm files are in ./resources/
      // this.initializationPromise = this.InitJavaParser(context).then(parser => {

      this.initializationPromise = this.InitJavaParser().then(() => {
        logger.info("InitJavaParser complete");
      });
    }
    return this.initializationPromise;
  }

  // tabby migration: context: vscode.ExtensionContext removed
  // public async InitJavaParser(context: vscode.ExtensionContext): Promise<Parser> {
  public async InitJavaParser(): Promise<void> {
    logger.info("Start InitJavaParser");
    this.parser = await getParser("java")
    return;
  }

  // 查找当前光标位置处的跨文件对象关联信息
  public FindRelativeObject(
    // document: TextDocument,
    fileContent: string,
    filePath: string,
    languageId: string,
    // tabby migration: changed row to line to match interface
    line: number,
    column: number
  ): RelativeCodeObject | null {
    // tabby migration: DocumentInfo is likely vscode dependent and needs to be refactored or replaced
    // const { path, content } = new DocumentInfo(document);
    // const languageId = document.languageId;
    const path = filePath;
    const content = fileContent;

    const rootNode = this.getRootNode(content);
    if (!rootNode) {
      return null;
    }

    // 更新文件解析
    const currFile = this.parseJavaCode(path, content, rootNode);
    if (!currFile) {
      return null;
    }

    const typeFilter: Array<string> = ["identifier", "type_identifier"];
    // tabby migration: changed row to line
    const nodes = rootNode.descendantsOfType(
      typeFilter,
      { row: line, column: 0 },
      { row: line, column }
    );
    let candidateNode: Node | null;
    let identifier = "";
    if (nodes.length) {
      candidateNode = nodes[nodes.length - 1];
      identifier = candidateNode.text.trim();
    } else {
      // tabby migration: changed row to line
      candidateNode = rootNode.descendantForPosition({ row: line, column });
    }
    const nodeList: Node[] = [];
    while (candidateNode) {
      nodeList.push(candidateNode);
      candidateNode = candidateNode.parent;
    }
    if (nodeList.length === 0) {
      return null;
    }

    let targetPackageName: string | undefined = "";
    let targetObjectName = "";

    const currNode = nodeList[0];
    let methodNode: Node | null = null;
    let methodNodePos = 0;

    if (!currNode) {
      return null;
    }

    // 检查光标是否在方法中
    for (let i = 0; i < nodeList.length; i++) {
      const node = nodeList[i];
      if (
        node.type === "method_declaration" ||
        node.type === "constructor_declaration"
      ) {
        methodNode = node;
        methodNodePos = i;
        break;
      }
    }

    if (!methodNode) {
      return null;
    }

    let currClass = null;
    for (let j = methodNodePos; j < nodeList.length; j++) {
      const node = nodeList[j];
      if (node.type === "class_declaration") {
        const superNode = node.childForFieldName("superclass");
        if (superNode) {
          const superIdentifier = this.firstDescendantOfType(
            superNode,
            "type_identifier"
          );
          if (superIdentifier) {
            identifier = superIdentifier.text.trim();
          }
        }
        const classNameNode = this.firstDescendantOfType(node, "identifier");
        if (classNameNode) {
          const className = classNameNode.text.trim();
          currClass = currFile.objects.find(
            (jo) => jo.objectName === className
          );
          if (currClass) {
            break;
          }
        }
      }
    }

    if (!identifier) {
      return null;
    }

    if (currClass) {
      for (const field of currClass.fields) {
        if (identifier === field.fieldVariable) {
          targetPackageName = field.fieldPackage;
          targetObjectName = field.fieldName;
          break;
        }
      }
    }

    const packages = [...currFile.importPackages, currFile.packageName + ".*"];

    if (!targetPackageName) {
      const declaredVariables = this.getMethodVariableDeclarations(
        packages,
        methodNode,
        currNode
      );
      if (declaredVariables[identifier]) {
        targetPackageName = declaredVariables[identifier].packageName;
        targetObjectName = declaredVariables[identifier].typeName;
      }
    }

    if (!targetPackageName) {
      targetPackageName = this.getPackageByObjectName(
        packages,
        identifier
      )?.packageName;
      targetObjectName = identifier;
    }

    if (targetPackageName) {
      return this.objectSystem.queryByPackageAndObjectName(
        languageId,
        targetPackageName,
        targetObjectName
      );
    }

    return null;
  }

  /**
   * 此接口暴露给插件业务层调用的能力接口
   * 解释代码文件，将代码文件中的主要语法对象解释出来，并存入代码对象缓存索引系统，以提供后续跨文件对象关联使用
   * 本接口为插件启动时，以及代码工程有代码文件发生用户非输入代码更新时调用
   *
   * @param fileName        代码文件名称，为代码文件在代码工程内的路径
   * @param codeFileContent 代码文件内容
   */
  public async ParseFile(
    fileName: string,
    codeFileContent: string
  ): Promise<void> {
    this.parseJavaCode(fileName, codeFileContent, undefined);
  }

  private parseJavaCode(
    path: string,
    content: string,
    rootNode?: Node
  ): JavaFile | null {
    logger.debug(`[JavaParser] Parse Java Code in file: ${path}`);

    if (!rootNode) {
      logger.error("崩溃检测15:" + path);
      const newNode = this.getRootNode(content);
      logger.error("崩溃检测16:" + newNode == null);
      if (!newNode) {
        return null;
      }
      logger.debug(`parseJavaCode success, filePath:${path}`);
      rootNode = newNode;
    }

    let packageName = "";
    const importPackages: string[] = [];
    const objects: JavaObject[] = [];
    const objectsToUpdate: UpdateObject[] = [];

    for (let i = 0; i < rootNode.childCount; i++) {
      const node = rootNode.child(i);
      if (!node) {
        continue;
      }
      switch (node.type) {
        case "package_declaration": {
          const packageIdentifier = this.firstDescendantOfType(node, [
            "scoped_identifier",
            "identifier",
          ]);
          if (packageIdentifier) {
            packageName = packageIdentifier.text;
          } else {
            return null;
          }
          break;
        }
        case "import_declaration": {
          let importIdentifier = "";
          for (const child of node.children) {
            if (!child.isNamed) {
              continue;
            }
            switch (child.type) {
              case "identifier":
              case "scoped_identifier":
                importIdentifier += child.text;
                break;
              case "asterisk":
                importIdentifier += ".*";
                break;
              default:
                importIdentifier = "";
            }
            if (!importIdentifier) {
              break;
            }
          }
          if (importIdentifier) {
            importPackages.push(importIdentifier);
          }
          break;
        }
        // 将class, enum, interface 精简成Object内容存入缓存
        case "class_declaration":
        case "interface_declaration":
        case "enum_declaration": {
          const objectNameNode = this.firstDescendantOfType(node, "identifier");
          if (objectNameNode) {
            for (const descendantNode of [
              {
                packageSuffix: "",
                nodeIdentifierName: objectNameNode.text,
                node,
              },
              ...this.descendantsOfType(
                node,
                [
                  "class_declaration",
                  "interface_declaration",
                  "enum_declaration",
                ],
                objectNameNode.text
              ),
            ]) {
              const node = descendantNode.node;
              const objectName = descendantNode.nodeIdentifierName;
              /*****配合Go语言场景进行的改动******/
              // const objectType = node.type.replace('_declaration', '');
              const objectType = node.type;
              const [simpleText, fields] = this.extractJavaObject(
                node,
                importPackages
              );
              const javaObject: JavaObject = {
                objectName,
                objectType,
                simpleText,
                fields,
              };
              objects.push(javaObject);
              objectsToUpdate.push(
                new UpdateObject(
                  descendantNode.packageSuffix,
                  objectType,
                  objectName,
                  simpleText
                )
              );
            }
          }
          break;
        }
      }
    }

    if (!packageName) {
      return null;
    }

    this.objectSystem.updateByFile(packageName, path, objectsToUpdate);

    return { packageName, importPackages, objects };
  }

  private getRootNode(codeFileContent: string) {
    if (!this.parser) {
      logger.error("崩溃检测17" + codeFileContent);
      return null;
    }
    try {
      return this.parser.parse(codeFileContent).rootNode;
    } catch (error) {
      throw new Error(`Parsing error: ${error}`);
    }
  }

  private firstDescendantOfType(
    node: Node,
    types: string | Array<string>
  ): Node | null {
    if (!Array.isArray(types)) {
      types = [types];
    }
    for (const child of node.children) {
      if (types.includes(child.type)) {
        return child;
      }
    }
    return null;
  }

  private childrenOfType(
    node: Node,
    types: string | Array<string>
  ): Array<Node> {
    const children: Array<Node> = [];
    if (!Array.isArray(types)) {
      types = [types];
    }
    for (const child of node.children) {
      if (types.includes(child.type)) {
        children.push(child);
      }
    }
    return children;
  }

  private descendantsOfType(
    node: Node,
    types: string | Array<string>,
    parentNodeName = ""
  ): Array<DescendantNode> {
    const descendants: Array<DescendantNode> = [];
    if (!Array.isArray(types)) {
      types = [types];
    }
    for (const child of node.children) {
      let parentName = parentNodeName;
      if (types.includes(child.type)) {
        const childName = this.firstDescendantOfType(child, "identifier");
        if (childName) {
          descendants.push({
            packageSuffix: "." + parentNodeName,
            node: child,
            nodeIdentifierName: childName.text,
          });
          parentName = parentNodeName + "." + childName.text;
        }
      }
      descendants.push(...this.descendantsOfType(child, types, parentName));
    }
    return descendants;
  }

  private extractJavaObject(
    node: Node,
    importPackages: string[]
  ): [string, JavaField[]] {
    const fields: JavaField[] = [];
    const simpleText = this.extractNodesSimpleText(
      node.children,
      false,
      importPackages,
      fields
    );

    /*  for (let i = 0; i < node.childCount; i++) {
      const child = node.child(i);
      if (!child) {
        continue;
      }
      switch (child.type) {
        case 'modifiers':
        case 'class':
        case 'identifier':
        case 'superclass':
          simpleText += child.text.trim() + ' ';
          break;
        case 'super_interfaces':
          for (let j = 0; j < child.childCount; j++) {
            const c = child.child(j);
            if (!c) {
              continue;
            }
            if (c.type === 'implements' || c.type === 'type_list') {
              simpleText += c.text.trim() + ' ';
            }
          }
          break;
        case 'class_body':
        case 'enum_body':
        case 'interface_body':
          for (let j = 0; j < child.childCount; j++) {
            const c = child.child(j);
            if (!c) {
              continue;
            }
            switch (c.type) {
              case '{':
              case '}':
                simpleText += c.type + '\n';
                break;
              case 'field_declaration':
                fields.push(this.extractFields(c, importPackages));
                simpleText += '\t' + c.text.trim() + '\n';
                break;
              case 'method_declaration':
              case 'constructor_declaration': {
                const noBodyNodes = c.children.slice(0, -1);
                for (const childNode of noBodyNodes) {
                  switch (childNode.type) {
                    case 'modifiers':
                      simpleText += '\t' + childNode.text.trim() + ' ';
                      break;
                    case 'identifier':
                      simpleText += childNode.text.trim();
                      break;
                    default:
                      simpleText += childNode.text.trim() + ' ';
                      break;
                  }
                }
                if (node.type === 'class_declaration' || node.type === 'enum_body') {
                  simpleText += '{}\n';
                } else {
                  simpleText += ';\n';
                }
                break;
              }
              case 'enum_constant':
                simpleText += '\t' + c.text.trim() + ';\n';
                break;
            }
          }
          break;
      }
   } */

    return [simpleText, fields];
  }

  private extractNodesSimpleText(
    nodes: Node[],
    all = false,
    importPackages: string[] = [],
    fields: JavaField[] = []
  ): string {
    let simpleText = "";
    for (const node of nodes) {
      switch (node.type) {
        case "modifiers":
        case "class":
        case "identifier":
        case "superclass":
        case "super_interfaces":
          simpleText += node.text.trim() + " ";
          break;
        case "class_body":
        case "interface_body":
          simpleText += this.extractNodesSimpleText(
            node.children,
            false,
            importPackages,
            fields
          );
          break;
        case "enum_body": {
          simpleText += "{\n";
          const enums = this.childrenOfType(node, "enum_constant");
          for (let i = 0; i < enums.length; i++) {
            if (i === enums.length - 1) {
              simpleText += "\t" + enums[i].text.trim() + ";\n";
            } else {
              simpleText += "\t" + enums[i].text.trim() + ",\n";
            }
          }
          const declarations = this.childrenOfType(
            node,
            "enum_body_declarations"
          );
          for (const declaration of declarations) {
            simpleText += this.extractNodesSimpleText(
              declaration.children,
              false,
              importPackages,
              fields
            );
          }
          simpleText += "\n}";
          break;
        }
        case "{":
        case "}":
          simpleText += node.type + "\n";
          break;
        case "field_declaration":
          if (!this.isPrivateModifiers(node)) {
            fields.push(this.extractFields(node, importPackages));
            simpleText += "\t" + node.text.trim() + "\n";
          }
          break;
        case "block":
        case "constructor_body":
          simpleText += "{}\n";
          break;
        case "method_declaration":
        case "constructor_declaration":
          if (!this.isPrivateModifiers(node)) {
            simpleText += "\t";
            simpleText += this.extractNodesSimpleText(node.children, true);
          }
          break;
        default:
          if (all || !node.isNamed) {
            simpleText += node.text.trim() + " ";
          }
      }
    }
    return simpleText;
  }

  private isPrivateModifiers(node: Node) {
    return this.childrenOfType(node, "modifiers")[0]?.text.includes("private");
  }

  private extractFields(node: Node, importPackages: string[]): JavaField {
    let fieldVariable = "";

    let fieldPackage = "",
      fieldName = "";
    const typeIdentifier = this.firstDescendantOfType(node, "type_identifier");
    if (typeIdentifier) {
      fieldName = typeIdentifier.text.trim();
    }

    const variableDeclarator = this.firstDescendantOfType(
      node,
      "variable_declarator"
    );
    if (variableDeclarator) {
      const identifier = this.firstDescendantOfType(
        variableDeclarator,
        "identifier"
      );
      if (identifier) {
        fieldVariable = identifier.text.trim();
      }
    }

    const objectPackage = this.getPackageByObjectName(
      importPackages,
      fieldName
    );

    if (objectPackage) {
      fieldPackage = objectPackage.packageName;
      fieldName = objectPackage.typeName;
    }

    return {
      fieldPackage,
      fieldName,
      fieldVariable,
    };
  }

  private getPositionNodeList(
    rootNode: Node,
    row: number,
    column: number,
    filter: Array<string>
  ): Node[] {
    const nodeList: Node[] = [];
    const nodes = rootNode.descendantsOfType(
      filter,
      { row, column: 0 },
      { row, column }
    );
    if (nodes.length) {
      let candidateNode: Node | null = nodes[nodes.length - 1];
      while (candidateNode) {
        nodeList.push(candidateNode);
        candidateNode = candidateNode.parent;
      }
    }
    return nodeList;
  }

  private getMethodVariableDeclarations(
    importPackages: string[],
    methodNode: Node,
    identifierNode: Node
  ): Variables {
    let variables: Variables = {};
    // 获取本地变量声明节点
    for (const node of methodNode.descendantsOfType(
      [
        "formal_parameter",
        "local_variable_declaration",
        "enhanced_for_statement",
        "catch_formal_parameter",
      ],
      methodNode.startPosition,
      identifierNode.startPosition
    )) {
      variables = {
        ...variables,
        ...this.getNodeVariableDeclaration(node, importPackages),
      };
    }

    return variables;
  }

  private getNodeVariableDeclaration(
    node: Node,
    importPackages: string[]
  ): Variables {
    const variable: Variables = {};
    let typeNode: Node | null = null;
    let variableName: string | undefined;
    switch (node.type) {
      case "formal_parameter":
      case "local_variable_declaration": {
        typeNode = node.childForFieldName("type");
        const declaratorNode = node.childForFieldName("declarator");
        if (declaratorNode) {
          variableName = declaratorNode.childForFieldName("name")?.text;
        } else {
          variableName = node.childForFieldName("name")?.text;
        }
        break;
      }
      case "enhanced_for_statement": {
        typeNode = node.childForFieldName("type");
        variableName = node.childForFieldName("name")?.text;
        break;
      }
      case "catch_formal_parameter": {
        for (const child of node.children) {
          if (child.type === "catch_type") {
            typeNode = child.firstNamedChild;
          }
        }
        variableName = node.childForFieldName("name")?.text;
        break;
      }
      default:
        return variable;
    }
    // 获取变量类型
    if (typeNode && variableName) {
      let typeName = "";
      switch (typeNode.type) {
        case "type_identifier":
        case "scoped_type_identifier":
          typeName = typeNode.text.trim();
          break;
        case "generic_type": {
          const typeNodes = typeNode.descendantsOfType("type_identifier");
          if (typeNodes.length) {
            typeName = typeNodes[0].text.trim();
          }
          break;
        }
      }
      if (typeName) {
        const objectPackage = this.getPackageByObjectName(
          importPackages,
          typeName
        );
        if (objectPackage) {
          variable[variableName] = objectPackage;
        }
      }
    }
    return variable;
  }

  private getPackageByObjectPath(
    objectPath: string
  ): VariableDeclaration | null {
    const splits = this.splitLastDot(objectPath);
    if (
      splits.length > 1 &&
      this.objectSystem.queryObjectInPackage(splits[0], splits[1])
    ) {
      return { packageName: splits[0], typeName: splits[1] };
    }
    return null;
  }

  private splitLastDot(objectPath: string): string[] {
    const lastDotIndexObject = objectPath.lastIndexOf(".");
    if (lastDotIndexObject === -1) {
      return [objectPath];
    }
    return [
      objectPath.slice(0, lastDotIndexObject),
      objectPath.slice(lastDotIndexObject + 1),
    ];
  }

  private getPackageByObjectName(
    importPackages: string[],
    objectName: string
  ): VariableDeclaration | null {
    const objectPackage = this.getPackageByObjectPath(objectName);
    if (objectPackage) {
      return objectPackage;
    }
    // const prefixObject = this.splitLastDot(objectName)[0];
    for (const importPackage of importPackages) {
      const [importPackageName, importObject] =
        this.splitLastDot(importPackage);
      if (!importObject) {
        continue;
      }
      if (importObject === "*") {
        const objectPackage = this.getPackageByObjectPath(
          importPackageName + "." + objectName
        );
        if (objectPackage) {
          return objectPackage;
        }
      } else {
        let firstObject = objectName;
        const lastDotIndexObject = objectName.indexOf(".");
        let path = importPackage;
        if (lastDotIndexObject > -1) {
          firstObject = objectName.slice(0, lastDotIndexObject);
          path = importPackage + "." + objectName.slice(lastDotIndexObject + 1);
        }
        if (importObject !== firstObject) {
          continue;
        }
        const objectPackage = this.getPackageByObjectPath(path);
        if (objectPackage) {
          return objectPackage;
        }
      }
    }
    return null;
  }
}

// 补充：JavaFile、JavaObject、JavaField 类定义
interface JavaFile {
  packageName: string;
  importPackages: string[];
  objects: JavaObject[];
}

interface JavaObject {
  objectName: string;
  objectType: string;
  simpleText: string;
  fields: JavaField[];
}

interface JavaField {
  fieldPackage: string;
  fieldName: string;
  fieldVariable: string;
}
