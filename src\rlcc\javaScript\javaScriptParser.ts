import * as fs from 'fs';
import { logger } from "../../logger";
// tabby migration: removed vscode specific imports as they are no longer needed.
// import * as vscode from 'vscode';
import Parser = require('web-tree-sitter');
// tabby migration: removed DocumentInfo import as it's no longer needed.
// import { DocumentInfo } from '../DocumentInfo';
// import { TextDocument, Uri } from 'vscode';
import * as path from 'path';
import CodeObjectIndexSystem from '../codeObjectIndexSystem';
import RelativeCodeObject from '../relativeCodeObject';
import { IRelativeCodeFinder, JSModuleSystem } from '../types';
import { UpdateObject } from '../updateObject';
// tabby migration: Re-adding fs import as it's now used directly.
import { inferLanguage } from '../../languages';
import { getParser } from "../../syntax/parser";

type Node = Parser.SyntaxNode;

// 解析import语句得到的object类型
interface ImportObject {
  name: string;
  alias: string;
  source: string;
}

enum InvokeType {
  Declarator,
  Parameter,
  Assignment,
}

interface IdentifierInvoke {
  type: InvokeType;
  node: Node;
  scopeNode: Node;
}

export class JavaScriptParser implements IRelativeCodeFinder {
  private parser: Parser | null;

  private objectSystem: CodeObjectIndexSystem;

  private initializationPromise: Promise<void> | null = null;

  public constructor(codeObjectIndexSystem: CodeObjectIndexSystem) {
    this.parser = null;
    this.objectSystem = codeObjectIndexSystem;
  }

  public initialize(workspaceRootPath?: string): Promise<void> {
    if (!this.initializationPromise) {
      this.initializationPromise = this.InitJavaScriptParser(workspaceRootPath).then(() => {
        logger.info("InitJavaScriptParser complete");
      });
    }
    return this.initializationPromise;
  }

  public async InitJavaScriptParser(workspaceRootPath?: string): Promise<void> {
    logger.info("Start InitJavaScriptParser");
    this.parser = await getParser("javascript")
    return;
  }

  public FindRelativeObject(
    fileContent: string,
    filePath: string,
    languageId: string,
    line: number,
    column: number
  ): RelativeCodeObject | null {
    const languageExt = languageId;
    const path = filePath;
    const content = fileContent;
    const rootNode = this.getRootNode(content);
    if (!rootNode) {
      return null;
    }

    const currFile = this.parseJavaScriptCode(path, content, rootNode);
    if (!currFile) {
      return null;
    }

    const importObjects = this.parseImportStatements(rootNode, path, line, column);
    const identifierImportObjects = this.findIdentifierImports(
      rootNode,
      line,
      column,
      importObjects
    );
    if (identifierImportObjects.length > 0) {
      logger.debug(
        `[JavaScriptParser] default import: ${identifierImportObjects[0].source} ${identifierImportObjects[0].name}`
      );
      return this.objectSystem.queryByPackageAndObjectName(
        languageExt,
        identifierImportObjects[0].source,
        identifierImportObjects[0].name
      );
    }
    return null;
  }

  // 根据用户光标的输入找到导入的object
  private findIdentifierImports(
    rootNode: Node,
    row: number,
    column: number,
    importObjects: ImportObject[]
  ): ImportObject[] {
    const identifierImportObjects: ImportObject[] = [];

    const nodes = rootNode.descendantsOfType('identifier', { row, column: 0 }, { row, column });
    let candidateNode: Node | null;
    let identifier = '';
    if (this.parser && nodes.length) {
      candidateNode = nodes[nodes.length - 1];
      identifier = candidateNode.text.trim();

      // 若当前光标变量为import的别名，则返回该import对象
      const candidateObject = this.findImportObject(importObjects, identifier);
      if (candidateObject) {
        return [candidateObject];
      }

      // 如果不是，检查当前标识符是否出现在变量声明、函数参数和赋值表达式中。
      const query = this.parser.getLanguage().query(`
      (variable_declarator
            name: (identifier) @declarator (#eq? @declarator "${identifier}")
      )
      (_ parameter: (_)@param (#eq? @param "${identifier}"))
      (_ parameters: (_ (identifier)@param (#eq? @param "${identifier}")) )
      (assignment_expression left: (_)@assignment (#eq? @assignment "${identifier}"))
      `);
      const matches = query.matches(rootNode, {
        startPosition: { row: 0, column: 0 },
        endPosition: { row, column },
      });
      const invokeChain: IdentifierInvoke[] = [];
      const outOfScope: IdentifierInvoke[] = [];
      let currentDeclarator: Node | null = null;
      const identifierScope = this.scopeNode(candidateNode);
      for (const match of matches) {
        for (const capture of match.captures) {
          const node = capture.node;
          switch (capture.name) {
            case 'declarator': {
              const declarator = this.addDeclaratorInvoke(
                node,
                identifierScope,
                invokeChain,
                outOfScope,
                InvokeType.Declarator
              );
              if (declarator) {
                currentDeclarator = declarator;
              }
              break;
            }
            case 'param': {
              const declarator = this.addDeclaratorInvoke(
                node,
                identifierScope,
                invokeChain,
                outOfScope,
                InvokeType.Parameter
              );
              if (declarator) {
                currentDeclarator = declarator;
              }
              break;
            }
            case 'assignment': {
              let assigmentScope: Node | null = this.scopeNode(node);
              if (currentDeclarator && this.isInvokeInScope(currentDeclarator, assigmentScope)) {
                for (const identifierInvoke of outOfScope) {
                  if (this.isInvokeInScope(identifierInvoke.scopeNode, assigmentScope)) {
                    assigmentScope = null;
                    break;
                  }
                }
                if (assigmentScope) {
                  invokeChain.push({
                    node,
                    scopeNode: assigmentScope,
                    type: InvokeType.Assignment,
                  });
                }
              }
              break;
            }
          }
        }
      }
      // console.log(invokeChain);
      for (const invoke of invokeChain.reverse()) {
        const objects = this.findImportObjectsInIdentifierInvoke(invoke, rootNode, importObjects);
        identifierImportObjects.push(...objects);
      }
    }

    return identifierImportObjects;
  }

  private findImportObjectsInIdentifierInvoke(
    invoke: IdentifierInvoke,
    rootNode: Node,
    objects: ImportObject[]
  ): ImportObject[] {
    switch (invoke.type) {
      case InvokeType.Declarator:
        return this.findImportObjectsInExpression(
          invoke.node.parent?.childForFieldName('value') || null,
          rootNode,
          objects
        );
      case InvokeType.Assignment:
        return this.findImportObjectsInExpression(
          invoke.node.parent?.childForFieldName('right') || null,
          rootNode,
          objects
        );
      case InvokeType.Parameter:
        // todo 处理函数入参场景，需要考虑全文中该函数调用
        return [];
    }
  }

  private findImportObjectsInExpression(
    expression: Node | null,
    rootNode: Node,
    importObjects: ImportObject[]
  ): ImportObject[] {
    const objects: ImportObject[] = [];
    if (expression) {
      switch (expression.type) {
        case 'new_expression': {
          const constructor = expression.childForFieldName('constructor')?.text?.trim();
          const importObject = this.findImportObject(importObjects, constructor);
          if (importObject) {
            objects.push(importObject);
          }
          break;
        }
        case 'identifier':
          return this.findIdentifierImports(
            rootNode,
            expression.endPosition.row,
            expression.endPosition.column,
            importObjects
          );
      }
    }
    return objects;
  }

  private findImportObject(importObjects: ImportObject[], identifier: string | undefined) {
    return importObjects.find(it => it.alias === identifier);
  }

  private addDeclaratorInvoke(
    node: Node,
    identifierScope: Node,
    invokeChain: IdentifierInvoke[],
    outOfScope: IdentifierInvoke[],
    type: InvokeType
  ): Node | null {
    const scopeNode = this.scopeNode(node, type === InvokeType.Parameter);
    if (this.isInvokeInScope(scopeNode, identifierScope)) {
      invokeChain.length = 0;
      outOfScope.length = 0;
      invokeChain.push({ type, node, scopeNode });
      return scopeNode;
    } else {
      outOfScope.push({ type, node, scopeNode });
      return null;
    }
  }

  private scopeNode(node: Node, param = false): Node {
    if (param) {
      while (node.parent) {
        const bodyField = node.childForFieldName('body');
        if (bodyField && bodyField.type === 'statement_block') {
          return bodyField;
        }
        node = node.parent;
      }
    } else {
      while (node.type !== 'statement_block' && node.parent) {
        node = node.parent;
      }
    }
    return node;
  }

  private isInvokeInScope(declaratorScope: Node, invokeScope: Node): boolean {
    while (declaratorScope.id !== invokeScope.id && invokeScope.parent) {
      invokeScope = this.scopeNode(invokeScope.parent);
    }
    return declaratorScope.id === invokeScope.id;
  }

  // 解析不同类型的import语句，得到导入object名和导入文件路径
  private parseImportStatements(
    rootNode: Node,
    currentFilePath: string,
    row: number,
    column: number
  ): ImportObject[] {
    const importObjects: ImportObject[] = [];
    // 识别import_statement类型的导入语句(ES6)
    const importNodes = rootNode.descendantsOfType('import_statement');

    for (const importNode of importNodes) {
      const sourceNode = importNode.childForFieldName('source');

      if (sourceNode) {
        const source = this.resolveSource(currentFilePath, sourceNode.text.slice(1, -1));
        if (source) {
          const importClauseNodes = importNode.descendantsOfType('import_clause');
          for (const importClauseNode of importClauseNodes) {
            for (const child of importClauseNode.children) {
              switch (child.type) {
                // 举例： import {x,y,z}
                case 'named_imports':
                  for (const specifier of child.descendantsOfType('import_specifier')) {
                    const name = specifier.childForFieldName('name');
                    if (name) {
                      const alias = specifier.childForFieldName('alias')?.text;
                      importObjects.push({
                        name: name.text,
                        alias: alias || name.text,
                        source: source,
                      });
                    }
                  }
                  break;
                // 举例：import * as all
                case 'namespace_import':
                  {
                    const identifier = child.children.find(it => it.type === 'identifier');
                    if (identifier) {
                      importObjects.push({
                        name: '',
                        alias: identifier.text,
                        source: source,
                      });
                    }
                  }
                  break;
                // 举例：import defaultExport
                case 'identifier':
                  importObjects.push({
                    name: 'default',
                    alias: child.text,
                    source: source,
                  });
                  break;
              }
            }
          }
        }
      }
    }

    // 识别require写法的导入语句(commonJS)
    if (this.parser) {
      const query = this.parser.getLanguage().query(`
      (variable_declarator
        value: (call_expression
          function: (identifier) @func (#eq? @func "require")
          arguments: (arguments (string (string_fragment) @source)
          )
        )
      ) @declarator
    `);
      const matches = query.matches(rootNode, {
        startPosition: { row: 0, column: 0 },
        endPosition: { row, column },
      });

      for (const match of matches) {
        let source = '';
        const tmpObjects: ImportObject[] = [];
        for (const capture of match.captures) {
          const node = capture.node;
          switch (capture.name) {
            case 'source': {
              source = this.resolveSource(currentFilePath, node.text) || '';
              break;
            }
            case 'declarator': {
              const nameNode = node.childForFieldName('name');
              if (nameNode) {
                switch (nameNode.type) {
                  // 举例：const arrowFunc = require('./export/arrowFunc.cjs')
                  case 'identifier':
                    tmpObjects.push({
                      name: '',
                      alias: nameNode.text,
                      source,
                    });
                    break;
                  case 'object_pattern':
                    for (const child of nameNode.children) {
                      switch (child.type) {
                        // 举例: const {sayhi, score} = require('./export/object.cjs')
                        case 'shorthand_property_identifier_pattern':
                          tmpObjects.push({ name: child.text, alias: child.text, source });
                          break;
                        // 举例：const {name, ...rest} = require('./export/object.cjs')
                        case 'rest_pattern': {
                          const identifier = child.children.find(
                            node => node.type === 'identifier'
                          );
                          if (identifier) {
                            tmpObjects.push({ name: '', alias: identifier.text, source });
                          }
                          break;
                        }
                        // 举例：const {greet: greet_rename, score: score_rename} = require('./export/object.cjs')
                        case 'pair_pattern': {
                          const keyField = child.childForFieldName('key');
                          const valueField = child.childForFieldName('value');
                          let key = '',
                            value = '';
                          if (keyField?.type === 'property_identifier') {
                            key = keyField.text;
                          } else if (keyField?.type === 'string') {
                            key = keyField.text.slice(1, -1);
                          }
                          if (valueField?.type === 'identifier') {
                            value = valueField.text;
                          }
                          if (key && value) {
                            tmpObjects.push({ name: key, alias: value, source });
                          }
                          break;
                        }
                      }
                    }
                    break;
                }
              }
            }
          }
        }
        if (source) {
          tmpObjects.forEach(object => (object.source = source));
          importObjects.push(...tmpObjects);
        }
      }
    }

    return importObjects;
  }

  // 解析import语句中的文件路径，返回这个导入文件的绝对路径
  private resolveSource(currentFilePath: string, source: string): string | null {
    if (source.startsWith('./') || source.startsWith('../') || source.startsWith('/')) {
      let basePath = source.startsWith('/')
        ? source
        : path.resolve(path.dirname(currentFilePath), source);

      const extensions = ['.js', '.jsx', '.json', '.node', '.mjs', '.cjs'];
      const ext = extensions.find(extension => basePath.endsWith(extension));
      if (ext) {
        basePath = basePath.slice(0, -ext.length);
      }
      for (const ext of extensions) {
        const resolvedPath = `${basePath}${ext}`;
        if (fs.existsSync(resolvedPath)) {
          return resolvedPath;
        }
      }
    }
    return null;
  }

  public async ParseFile(fileName: string, codeFileContent: string): Promise<void> {
    const currFile = await this.parseJavaScriptCode(fileName, codeFileContent, undefined);
    if (!currFile) {
      logger.debug(`GoParser ParseFile skip,code file parse is null.`);
    } else {
      this.objectSystem.updateByFile(
        currFile.filePath,
        currFile.filePath,
        currFile.objectsToUpdate
      );
    }
  }

  private async parseJavaScriptCode(
    path: string,
    content: string,
    rootNode?: Node
  ): Promise<JavaScriptFile | null> {
    logger.debug(`[JavaScriptParser] Parse JavaScript Code in file: ${path}`);
    // logger.debug(`[JavaScriptParser] Content: ${content}`);

    if (!rootNode) {
      const newNode = this.getRootNode(content);
      if (!newNode) {
        return null;
      }
      logger.debug(`parseJavaScriptCode success, filePath:${path}`);
      rootNode = newNode;
    }

    let objects: JavaScriptObject[] = [];
    // objectsToUpdate: 准备存入缓存的object
    // objectsNamedIdentifierExport: 记录namedIdentifier的导出, 用于之后根据identifier找对应的object
    // objectsDeclaration: 记录有哪些声明的object/函数/类， 用于之后根据identifier找对应的object
    const objectsToUpdate: UpdateObject[] = [];
    const objectsNamedIdentifierExport: string[] = [];
    const objectsDeclaration: JavaScriptObject[] = [];

    for (let i = 0; i < rootNode.childCount; i++) {
      const node = rootNode.child(i);
      if (!node) {
        continue;
      }
      switch (node.type) {
        // 解析export语句
        case 'export_statement': {
          const exportObjects = await this.processExportStatement(
            path,
            node,
            objectsNamedIdentifierExport
          );
          objects = objects.concat(exportObjects);
          break;
        }
        // 解析require语句
        case 'expression_statement':
          const exportObjects = this.processCJSExport(node, objectsNamedIdentifierExport);
          objects = objects.concat(exportObjects);
          break;
        // 解析声明的类/函数/生成器函数
        case 'class_declaration':
        case 'function_declaration':
        case 'generator_function_declaration': {
          const objectNameNode = this.firstDescendantOfType(node, 'identifier');
          const objectName = objectNameNode?.text.trim() || '';
          const objectType = node.type.replace('_declaration', '');
          const simpleText = this.extractNodesSimpleText(node.children);
          const candidateSimpleText = simpleText;
          const javaScriptObject: JavaScriptObject = {
            objectName,
            objectType,
            simpleText,
            candidateSimpleText,
          };
          objectsDeclaration.push(javaScriptObject);
          break;
        }
        // 解析const/let/var声明的变量
        case 'lexical_declaration':
        case 'variable_declaration': {
          const objectNameNode = node.descendantsOfType('identifier')[0];
          if (objectNameNode) {
            const objectName = objectNameNode.text.trim();
            const variableNode = this.firstDescendantOfType(node, 'variable_declarator');
            if (variableNode) {
              const objectNode = this.firstDescendantOfType(variableNode, [
                'class',
                'function_expression',
                'object',
                'arrow_function',
                'generator_function',
                'call_expression',
              ]);
              if (!objectNode) {
                continue;
              }
              let objectType;
              if (objectNode.type === 'call_expression') {
                objectType = objectNode.type; // 保留原始类型
              } else {
                objectType = objectNode.type.replace('_expression', '');
              }
              const simpleText = this.extractNodesSimpleText(objectNode?.children || []);
              // 设置candidateSimpleText是因为在处理导出的export clause场景时，缓存中需要存入更多的信息
              const candidateSimpleText = this.extractNodesSimpleText(node?.children || []);
              const javaScriptObject: JavaScriptObject = {
                objectName,
                objectType,
                simpleText,
                candidateSimpleText,
              };
              objectsDeclaration.push(javaScriptObject);
            }
          }
          break;
        }
      }
    }

    // 处理命名导出，根据identifier找到对应的object结构
    objectsDeclaration.forEach(obj => {
      objectsNamedIdentifierExport.find(name => {
        if (name === obj.objectName) {
          const index = objects.findIndex(o => o.originalName === name);
          if (index !== -1) {
            // 如果是export clause场景，缓存中存candidateSimpleText
            if (objects[index].exportClause === true) {
              objects[index].simpleText += obj.candidateSimpleText;
            } else {
              objects[index].simpleText += obj.simpleText;
            }
            objects[index].objectType = obj.objectType;
          }
        }
      });
    });

    objects = this.extractCJSExport(objects);

    for (const object of objects) {
      if (!object.objectType) {
        object.objectType = ''; // 待定 如果不在可识别的objectType中现在设置为空字符串
      }
      objectsToUpdate.push(
        new UpdateObject('', object.objectType, object.objectName, object.simpleText)
      );
    }
    // 为每一个文件再单独存储一个object，该object的simpleText包含此文件的所有导出，objectName为空字符串''
    // 第一个''为objectsuffix(所有updateObject的suffix都是''),
    // 第二个''为objectType(因为是所有object的聚合体，没有可定义的type给它),
    // 第三个''为objectName
    // 第四个参数包含所有导出对象的objectName和simpleText
    const allExportsArray = objects.map(obj => obj.simpleText).join('');
    objectsToUpdate.push(new UpdateObject('', '', '', allExportsArray));
    // console.log(path, objectsToUpdate);
    return { filePath: path, objects: objects, objectsToUpdate: objectsToUpdate };
  }

  // 确保CommonJS模块中的default导出对象处于数组的末尾，而其他非CommonJS的模块导出对象排在前面。
  private extractCJSExport(objects: JavaScriptObject[]): JavaScriptObject[] {
    let modulePos = 0,
      objectsExtract: JavaScriptObject[] = [];
    for (let i = objects.length - 1; i >= 0; i--) {
      const moduleSystem = objects[i].moduleSystem;
      const objectName = objects[i].objectName;
      if (moduleSystem === JSModuleSystem.CJS && objectName === 'default') {
        modulePos = i;
        break;
      }
    }
    if (!modulePos) {
      return objects;
    }
    const objectsToFilter = objects.slice(0, modulePos);
    const objectsFiltered = objectsToFilter.filter(
      object => object.moduleSystem !== JSModuleSystem.CJS
    );
    const objectsDef = objects.slice(modulePos);
    objectsExtract = objectsFiltered.concat(objectsDef);
    return objectsExtract;
  }

  /*******实现export default中declaration、anonymous、assignment_expression等场景*******/
  private async processExportStatement(
    path: string,
    rNode: Node,
    objectsNamedIdentifierExport: string[]
  ): Promise<JavaScriptObject[]> {
    let objectName = '',
      simpleText = '',
      moduleSystem = JSModuleSystem.ESM,
      exportObjects: JavaScriptObject[] = [];
    for (let j = 0; j < rNode.childCount; j++) {
      const node = rNode.child(j);
      if (!node) {
        continue;
      }
      // 重导出场景
      if (node.type == 'from') {
        exportObjects = await this.parseReExportStatement(path, rNode);
        return exportObjects;
      }
    }
    for (let i = 0; i < rNode.childCount; i++) {
      const node = rNode.child(i);
      if (!node) {
        continue;
      }
      switch (node.type) {
        case 'default':
          objectName = node.text.trim();
          simpleText += node.text.trim() + ' ';
          break;
        case 'export':
          simpleText += node.text.trim() + ' ';
          break;
        case 'class_declaration':
        case 'function_declaration':
        case 'generator_function_declaration': {
          // default&named导出场景，export语句中声明的类/函数/生成器函数
          const objectNameNode = this.firstDescendantOfType(node, 'identifier');
          const objectType = node.type.replace('_declaration', '');
          objectName = objectName || objectNameNode?.text.trim()!;
          simpleText += this.extractNodesSimpleText(node.children);
          exportObjects.push({ objectName, objectType: objectType, simpleText, moduleSystem });
          break;
        }
        case 'class':
        case 'function_expression':
        case 'object':
        case 'arrow_function':
        case 'generator_function':
        case 'call_expression': {
          // default导出场景
          let objectType;
          if (node.type === 'call_expression') {
            objectType = node.type; // 保留原始类型
          } else {
            objectType = node.type.replace('_expression', '');
          }
          simpleText += this.extractNodesSimpleText(node.children);
          exportObjects.push({ objectName, objectType: objectType, simpleText, moduleSystem });
          break;
        }
        case 'assignment_expression':
          break;
        case 'identifier':
          // default导出场景， export后接identifier
          objectName = node.text.trim();
          objectsNamedIdentifierExport.push(objectName);
          exportObjects.push({
            objectName: 'default',
            originalName: objectName,
            simpleText,
            moduleSystem,
          });
          break;
        case 'export_clause':
          // named导出场景，export后接花括号，如export {a,b}
          exportObjects = this.parseNamedIdentifier(simpleText, node, objectsNamedIdentifierExport);
          break;
        case 'lexical_declaration':
        case 'variable_declaration':
          // export后接const/let/var声明
          const lexicalIdentifier = this.firstDescendantOfType(node, ['const', 'let', 'var']);
          simpleText += lexicalIdentifier?.text.trim() + ' ';
          const variableDeclarator = this.firstDescendantOfType(node, 'variable_declarator');
          if (variableDeclarator) {
            const identifierNode = this.firstDescendantOfType(variableDeclarator, 'identifier');
            const objectNode = this.firstDescendantOfType(variableDeclarator, [
              'class',
              'function_expression',
              'object',
              'arrow_function',
              'generator_function',
              'call_expression',
            ]);
            if (identifierNode && objectNode) {
              objectName = identifierNode.text.trim();
              let objectType;
              if (objectNode.type === 'call_expression') {
                objectType = objectNode.type; // 保留原始类型
              } else {
                objectType = objectNode.type.replace('_expression', '');
              }
              simpleText += identifierNode.text.trim() + ' ';
              simpleText += '= ';
              simpleText += this.extractNodesSimpleText(objectNode.children);
              exportObjects.push({ objectName, objectType: objectType, simpleText, moduleSystem });
            }
          }
        default:
          simpleText += this.extractNodesSimpleText(node.children);
          break;
      }
    }
    return exportObjects;
  }

  // 解析require语句，用于commonJS模块
  private processCJSExport(rNode: Parser.SyntaxNode, objectsNamedIdentifierExport: string[]) {
    let objectName = '',
      simpleText = '',
      objectSimpleText = '',
      expressionSimpleText = '',
      moduleSystem = JSModuleSystem.CJS,
      exportObjects: JavaScriptObject[] = [];
    for (let i = 0; i < rNode.childCount; i++) {
      const node = rNode.child(i);
      if (!node) {
        continue;
      }
      switch (node.type) {
        case 'assignment_expression':
          const moduleExportsNodes = node.descendantsOfType('member_expression');
          let isCJSExport = false;
          for (let i = 0; i < moduleExportsNodes.length; i++) {
            const identifierNode = moduleExportsNodes[i].firstChild;
            const identifierText = identifierNode?.text.trim();
            if (
              identifierNode &&
              identifierNode.type === 'identifier' &&
              (identifierText === 'exports' || identifierText === 'module')
            ) {
              isCJSExport = true;
              break;
            }
          }
          if (isCJSExport) {
            const objectNameNode = moduleExportsNodes[0].lastChild;
            objectName = objectNameNode?.text.trim() || '';
            // 举例：module.exports=....
            if (objectNameNode?.type === 'property_identifier' && objectName === 'exports') {
              objectName = 'default';
            }
            const exportObjectNode = node.lastChild;
            let originalName = '';
            // export等号右边为identifier的场景
            if (exportObjectNode?.type === 'identifier') {
              originalName = exportObjectNode?.text.trim();
              objectsNamedIdentifierExport.push(originalName);
            }
            // 等号右边为类/函数/生成器函数/箭头函数/对象/call_expression的场景
            const objectNode = this.firstDescendantOfType(node, [
              'class',
              'function_expression',
              'object',
              'arrow_function',
              'generator_function',
              'call_expression',
            ]);
            // objectExpressionNode是export等号左边的部分，也要存到simpleText里
            const objectExpressionNode = this.firstDescendantOfType(node, 'member_expression');
            objectSimpleText = this.extractNodesSimpleText(objectNode?.children || []);
            expressionSimpleText = this.extractNodesSimpleText(
              objectExpressionNode?.children || []
            ).replace(/\s+/g, '');
            simpleText = expressionSimpleText + '=' + objectSimpleText;
            exportObjects.push({ objectName, originalName, simpleText, moduleSystem });
          }
          break;
        default:
          break;
      }
    }
    return exportObjects;
    //to do CommonJS两次默认导出场景处理
  }

  // 工具函数，用于解析export后接花括号，如export {a,b}的场景
  private parseNamedIdentifier(
    simpleText: string,
    node: Node,
    objectsNamedIdentifierExport: string[]
  ) {
    const objects: JavaScriptObject[] = [];
    const namedExportNodes = node.descendantsOfType('export_specifier');
    for (const namedExport of namedExportNodes) {
      const identifiers = namedExport.descendantsOfType('identifier');
      const originalName = identifiers[0].text.trim();
      const aliasName = identifiers.length > 1 ? identifiers[1].text.trim() : null;
      objectsNamedIdentifierExport.push(originalName);
      objects.push({
        objectName: aliasName || originalName,
        originalName,
        simpleText: simpleText,
        moduleSystem: JSModuleSystem.ESM,
        exportClause: true,
      });
    }
    return objects;
  }

  private getRootNode(codeFileContent: string) {
    if (!this.parser) {
      return null;
    }
    try {
      return this.parser.parse(codeFileContent).rootNode;
    } catch (error) {
      throw new Error(`Parsing error: ${error}`);
    }
  }

  private firstDescendantOfType(node: Node, types: string | Array<string>): Node | null {
    if (!Array.isArray(types)) {
      types = [types];
    }
    for (const child of node.children) {
      if (types.includes(child.type)) {
        return child;
      }
    }
    return null;
  }

  // 根据node类型存simpleText
  private extractNodesSimpleText(nodes: Node[]): string {
    let simpleText = '';
    for (const node of nodes) {
      switch (node.type) {
        case 'modifiers':
        case 'class':
        case 'identifier':
        case 'property_identifier':
        case 'formal_parameters':
        case 'superclass':
        case '=>':
          simpleText += node.text.trim() + ' ';
          break;
        case 'pair':
        case 'object':
        case 'class_body':
        case 'arguments':
          simpleText += this.extractNodesSimpleText(node.children);
          break;
        case ':':
          simpleText += node.type + '\n';
          break;
        case '{':
        case '}':
          simpleText += node.type + '\n';
          break;
        case 'field_declaration':
          break;
        case 'block':
        case 'statement_block':
          simpleText += '{}\n';
          break;
        case 'member_expression':
        case 'function_expression':
          simpleText += this.extractNodesSimpleText(node.children);
          break;
        case 'variable_declarator':
        case 'arrow_function':
          simpleText += this.extractNodesSimpleText(node.children);
          break;
        case 'method_definition':
          //to do: construction构造函数保留函数体
          if (!this.isPrivateModifiers(node)) {
            const identifierNode = this.firstDescendantOfType(node, 'property_identifier');
            simpleText += '\t';
            if (identifierNode?.text === 'constructor') {
              simpleText += node.text + '\n';
            } else {
              simpleText += this.extractNodesSimpleText(node.children);
            }
          }
          break;
        default:
          if (!node.isNamed) {
            simpleText += node.text.trim() + ' ';
          }
      }
    }
    return simpleText;
  }

  private isPrivateModifiers(node: Node) {
    const identifier = node.child(0);
    if (identifier && identifier.type === 'private_property_identifier') {
      return true;
    }
    return false;
  }

  // 重导出场景解析
  private async parseReExportStatement(path: string, rNode: Node): Promise<JavaScriptObject[]> {
    const exportObjects: JavaScriptObject[] = [];
    let source: string | null = null,
      moduleSystem = JSModuleSystem.ESM;
    const reExportObjectNames: { targetName: string; originalName: string }[] = [];
    // 获取重导出的source
    const sourceNode = this.firstDescendantOfType(rNode, 'string');
    if (sourceNode) {
      source = this.resolveSource(path, sourceNode.text.slice(1, -1));
    }

    // 遍历rNode子节点，提取 targetName和originalName
    for (let i = 0; i < rNode.childCount; i++) {
      const node = rNode.child(i);
      if (!node) {
        continue;
      }

      switch (node.type) {
        // 举例：export * from './source'
        case '*':
          reExportObjectNames.push({ targetName: '', originalName: '' });
          break;
        // 举例：export {a,b} from './source'
        case 'export_clause': {
          const exportSpecifierNodes = node.descendantsOfType('export_specifier');
          exportSpecifierNodes.forEach(exportSpecifierNode => {
            const identifiers = exportSpecifierNode.descendantsOfType('identifier');
            const targetName = identifiers[0]?.text.trim();
            const originalName = identifiers[1]?.text.trim() || targetName;
            reExportObjectNames.push({ targetName: targetName, originalName: originalName });
          });
          break;
        }
        // 举例：export * as export_all from './source'
        case 'namespace_export': {
          const nameIdentifierNode = node.descendantsOfType('identifier');
          const originalName = nameIdentifierNode[0]?.text.trim();
          const targetName = '';
          reExportObjectNames.push({ targetName: targetName, originalName: originalName });
          break;
        }

        default:
          break;
      }
    }
    // 获取到source后，先查缓存，缓存中没有则解析source文件
    if (source) {
      // tabby migration: Replaced vscode.workspace.openTextDocument with Node.js fs.promises.readFile
      // to remove dependency on VS Code API.
      try {
        const sourceContent = await fs.promises.readFile(source, 'utf-8');
        const sourcePath = source;
        const languageExt = inferLanguage(sourcePath);

        let foundObjects = false;
        for (let i = 0; i < reExportObjectNames.length; i++) {
          const targetName = reExportObjectNames[i].targetName;
          const relativeObject = this.objectSystem.queryByPackageAndObjectName(
            languageExt,
            sourcePath,
            targetName
          );
          if (relativeObject) {
            exportObjects.push({
              objectName: reExportObjectNames[i].originalName,
              objectType: relativeObject.getObjectType(),
              simpleText: relativeObject.getObjectText(),
            });
            foundObjects = true;
          }
        }
        if (foundObjects) {
          return exportObjects;
        }
        const parsedObjects = await this.parseJavaScriptCode(sourcePath, sourceContent);

        if (parsedObjects) {
          parsedObjects.objects.forEach(parsedObject => {
            for (let i = 0; i < reExportObjectNames.length; i++) {
              const targetName = reExportObjectNames[i].targetName;
              const originalName = reExportObjectNames[i].originalName;
              if (targetName === '') {
                // 全部导出，无aliasName
                if (originalName === '') {
                  exportObjects.push(parsedObject);
                }
                // 全部导出，有aliasName
                else {
                  exportObjects.push({
                    objectName: originalName,
                    simpleText: parsedObjects.objects.map(obj => obj.simpleText).join(''),
                    objectType: '',
                    moduleSystem,
                  });
                }
              }
              // 根据花括号中的内容匹配特定导出
              else if (parsedObject.objectName === targetName && originalName) {
                exportObjects.push({
                  objectName: originalName,
                  simpleText: parsedObject.simpleText,
                  objectType: parsedObject.objectType,
                  moduleSystem,
                });
              }
            }
          });
        }
      } catch (err) {
        logger.error(`[JavaScriptParser] Error reading re-exported file ${source}: ${err}`);
      }
    }
    return exportObjects;
  }
}
// 补充：JavaScriptFile, JavaScriptObject, JavaScriptField 类定义
interface JavaScriptFile {
  objects: JavaScriptObject[];
  filePath: string;
  objectsToUpdate: UpdateObject[];
}

interface JavaScriptObject {
  objectName: string;
  originalName?: string;
  objectType?: string;
  simpleText: string;
  moduleSystem?: string;
  candidateSimpleText?: string;
  exportClause?: boolean;
}
