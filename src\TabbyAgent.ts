/* eslint-disable no-useless-catch */
import deepEqual from "deep-equal";
import { deepmerge } from "deepmerge-ts";
import { deleteProperty, getProperty, setProperty } from "dot-prop";
import { EventEmitter } from "events";
import { v4 as uuid } from "uuid";

import type {
  AbortSignalOption,
  Agent,
  AgentEvent,
  AgentInitOptions,
  AgentStatus,
  CompletionRequest,
  CompletionResponse,
} from "./Agent";
import {
  AgentConfig,
  defaultAgentConfig,
  PartialAgentConfig,
} from "./AgentConfig";
import { CompletionContext, UpdatedFile } from "./CompletionContext";
import { PassingParameters } from "./env";
import { logger } from "./logger";
import { postCacheProcess, preCacheProcess } from "./postprocess";
import { RagService } from "./ragService";
import ProjectCodeIndexer from "./rlcc/projectCodeIndexer";
import RelativeCodeObject from "./rlcc/relativeCodeObject";
import {
  abortSignalFromAnyOf,
  fetchPost,
  findUnpairedAutoClosingChars,
  isAlphanumeric,
  isBlank,
  isCommentLine,
  isImportOrPackageLine,
  MAX_LINE_LENGTH,
  MIN_PREFIX_CHARS,
} from "./utils";

/**
 * TabbyAgent 类 - 代码补全代理类
 *
 * 该类实现了Agent接口，是代码补全功能的核心实现类。主要功能包括：
 * 1. 管理与补全服务器的连接状态
 * 2. 处理代码补全请求和响应
 * 3. 维护补全缓存
 * 4. 处理用户认证
 * 5. 收集使用统计数据
 *
 * 主要组件：
 * - 日志记录器：用于记录系统运行日志
 * - 匿名使用统计：收集用户使用数据
 * - 配置管理：处理系统配置
 * - 状态管理：维护系统运行状态
 * - 问题追踪：记录和管理系统问题
 * - 健康检查：监控服务器连接状态
 * - 补全缓存：优化补全响应速度，减少服务器负载
 * - 补全防抖：控制补全请求频率
 *
 * 工作流程：
 * 1. 初始化系统配置和组件
 * 2. 建立与服务器的连接
 * 3. 处理用户认证（如需要）
 * 4. 接收并处理补全请求
 * 5. 返回补全结果
 * 6. 维护系统状态和统计数据
 */
export class TabbyAgent extends EventEmitter implements Agent {
  /**
   * 系统日志记录器实例
   * 用于记录系统运行时的各种日志信息，便于调试和问题追踪
   */
  private readonly logger = logger;

  /**
   * 系统当前配置
   * 存储所有系统运行相关的配置信息，默认使用defaultAgentConfig
   */
  public config: AgentConfig = defaultAgentConfig;

  /**
   * 客户端配置
   * 存储由initialize和updateConfig方法提供的配置信息
   * 这些配置会覆盖默认配置
   */
  private clientConfig: PartialAgentConfig = {};

  /**
   * 代理当前状态
   * 可能的值：
   * - notInitialized: 未初始化
   * - ready: 就绪
   * - disconnected: 连接断开
   * - unauthorized: 未授权
   * - finalized: 已终止
   */
  private status: AgentStatus = "notInitialized";

  /**
   * 非并行补全请求的中止控制器
   * 用于取消正在进行的补全请求，确保同时只有一个活跃的补全请求
   */
  private nonParallelProvideCompletionAbortController?: AbortController;

  // 在类的开头定义一个常量前缀
  private static readonly LOG_PREFIX = "[cpl-agent]";

  private ragService: RagService | null = null;

  private rlccService: ProjectCodeIndexer | null = null;

  /**
   * TabbyAgent 构造函数
   * 初始化代理实例并设置定时任务
   *
   * 主要工作：
   * 1. 调用父类EventEmitter构造函数
   * 2. 设置重试连接定时器 - 每30秒检查一次连接状态
   * 3. 设置统计数据提交定时器 - 每5分钟提交一次使用数据
   */
  constructor() {
    // 调用父类 EventEmitter 的构造函数
    super();
    process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = "0";
  }

  /**
   * 应用配置更改
   * 合并并更新系统配置，触发相关事件
   *
   * 处理流程：
   * 1. 保存旧配置和状态
   * 2. 合并默认配置和客户端配置
   * 3. 更新匿名统计设置
   * 4. 处理服务器配置变更
   * 5. 执行健康检查
   * 6. 触发配置更新事件
   *
   * @private
   */
  private async applyConfig() {
    this.config = deepmerge(
      defaultAgentConfig,
      this.clientConfig
    ) as AgentConfig;
    const event: AgentEvent = { event: "configUpdated", config: this.config };
    super.emit("configUpdated", event);
  }

  /**
   * 创建请求中止信号
   * 用于控制请求超时和手动取消
   *
   * 特性：
   * 1. 支持自定义超时时间
   * 2. 支持外部中止信号
   * 3. 确保超时值不超过系统限制
   *
   * @param options - 配置选项
   * @param options.signal - 可选的外部中止信号
   * @param options.timeout - 可选的超时时间(毫秒)
   * @returns 合并后的中止信号
   * @private
   */
  private createAbortSignal(options?: {
    signal?: AbortSignal;
    timeout?: number;
  }): AbortSignal {
    // 获取超时时间，使用传入的timeout或配置的requestTimeout
    // 0x7fffffff (2147483647) 是32位有符号整数的最大值
    // 使用 Math.min 确保超时值不会超过这个限制
    const timeout = Math.min(
      0x7fffffff,
      options?.timeout || this.config.server.requestTimeout
    );

    // 创建一个新的中止信号，它会在以下任一情况发生时触发:
    // 1. 达到指定的超时时间
    // 2. 外部信号被触发(如果提供了的话)
    return abortSignalFromAnyOf([
      AbortSignal.timeout(timeout), // 超时信号
      options?.signal, // 可选的外部信号
    ]);
  }

  /**
   * 创建补全上下文的文本段
   * 处理并优化用于代码补全的上下文信息
   *
   * 处理内容：
   * 1. 根据模式配置裁剪前缀和后缀长度
   * 2. 处理自动闭合字符
   * 3. 处理剪贴板内容
   *
   * @param context - 补全上下文
   * @returns 处理后的文本段
   * @private
   */
  private createSegments(context: CompletionContext): {
    prefix: string;
    suffix: string;
    clipboard?: string;
  } {
    const { prefixLines, suffixLines } = context;

    // logger.debug("prefixLines: " + JSON.stringify(prefixLines));
    // logger.debug("suffixLines: " + JSON.stringify(suffixLines));
    let maxPrefixLines = Infinity;
    let maxSuffixLines = Infinity;
    if (global.passingParameters.serverType !== "codefree") {
      //codefree 不限制前缀和后缀的行数
      // max lines in prefix and suffix configurable
      maxPrefixLines =
        this.config.server.cplMode === "速度优先"
          ? 50
          : this.config.server.cplMode === "平衡模式"
            ? 100
            : this.config.server.cplMode === "精准优先"
              ? 150
              : 100;

      maxSuffixLines =
        this.config.server.cplMode === "速度优先"
          ? 50
          : this.config.server.cplMode === "平衡模式"
            ? 100
            : this.config.server.cplMode === "精准优先"
              ? 150
              : 100;
    }

    const prefix = prefixLines
      .slice(Math.max(prefixLines.length - maxPrefixLines, 0))
      .join("");
    let suffix;
    if (
      this.config.completion.prompt.experimentalStripAutoClosingCharacters &&
      context.mode !== "fill-in-line"
    ) {
      suffix = "\n" + suffixLines.slice(1, maxSuffixLines).join("");
    } else {
      suffix = suffixLines.slice(0, maxSuffixLines).join("");
    }

    let clipboard = undefined;
    const clipboardConfig = this.config.completion.prompt.clipboard;
    if (
      context.clipboard.length >= clipboardConfig.minChars &&
      context.clipboard.length <= clipboardConfig.maxChars
    ) {
      clipboard = context.clipboard;
    }
    return { prefix, suffix, clipboard };
  }

  /**
   * 初始化代理实例
   * 设置数据存储、配置和统计信息
   *
   * 初始化流程：
   * 1. 设置数据存储实例
   * 2. 初始化匿名使用统计
   * 3. 设置客户端属性
   * 4. 应用初始配置
   * 5. 记录初始化事件
   *
   * @param options - 初始化选项
   * @returns 是否初始化成功
   */
  public async initialize(options: AgentInitOptions): Promise<boolean> {
    // 不能重复初始化
    if (this.status !== "notInitialized") {
      return false;
    }

    // 如果传了配置，则更新配置
    if (options.config) {
      this.clientConfig = options.config;
      await this.applyConfig();
    }

    return true;
  }

  /**
   * 终止代理实例
   * 清理资源并停止所有活动
   *
   * 终止流程：
   * 1. 提交最终统计数据
   * 2. 清理定时器
   * 3. 更新状态为已终止
   *
   * @returns 是否成功终止
   */
  public async finalize(): Promise<boolean> {
    if (this.status === "finalized") {
      return false;
    }
    return true;
  }

  /**
   * 更新配置
   * 修改指定配置项的值
   *
   * @param key - 配置键
   * @param value - 配置值
   * @returns 是否更新成功
   */
  public async updateConfig(key: string, value: any): Promise<boolean> {
    const current = getProperty(this.clientConfig, key);
    if (!deepEqual(current, value)) {
      if (value === undefined) {
        deleteProperty(this.clientConfig, key);
      } else {
        setProperty(this.clientConfig, key, value);

        // Update global.passingParameters when server.endpoint is updated
        if (key === "server.endpoint") {
          global.passingParameters.serverBaseUrl = value;
          logger.info(`Updated serverBaseUrl to: ${value}`);
        }
      }
      await this.applyConfig();
    }
    return true;
  }

  /**
   * 清除配置
   * 删除指定的配置项
   *
   * @param key - 配置键
   * @returns 是否清除成功
   */
  public async clearConfig(key: string): Promise<boolean> {
    return await this.updateConfig(key, undefined);
  }

  /**
   * 获取当前配置
   * 返回完整的配置对象
   *
   * @returns 当前配置
   */
  public getConfig(): AgentConfig {
    return this.config;
  }

  /**
   * 获取当前环境变量
   */
  public getEnvParameters(): PassingParameters {
    return global.passingParameters;
  }

  /**
   * 获取当前状态
   * 返回代理的运行状态
   *
   * @returns 当前状态
   */
  public getStatus(): AgentStatus {
    return this.status;
  }

  /**
   * 检查是否应该中止自动补全
   * 根据上下文判断是否应该触发代码补全
   *
   * 检查条件：
   * 1. 行长度是否超过限制
   * 2. 是否是注释行
   * 3. 是否是导入/包声明
   * 4. 前缀长度是否足够
   * 5. 最后字符是否合法
   * 6. 括号是否匹配
   *
   * @param context - 补全上下文
   * @returns 是否应该中止
   */
  public shouldAbortAutoCompletion(context: CompletionContext): boolean {
    const { language, currentLinePrefix, prefixLines, currentLineSuffix } =
      context;

    // 检查1: 行长度阈值
    const fullLine = currentLinePrefix + currentLineSuffix;
    if (fullLine.length > MAX_LINE_LENGTH) {
      return true;
    }

    // 检查2: 注释行检查
    if (isCommentLine(currentLinePrefix, language)) {
      return true;
    }

    // 检查3: 导入/包声明检查
    if (isImportOrPackageLine(currentLinePrefix, language, prefixLines)) {
      return true;
    }

    // 检查4: 最小前缀长度
    const trimmedPrefix = currentLinePrefix.trim();
    if (trimmedPrefix.length < MIN_PREFIX_CHARS) {
      return true;
    }

    // 检查5: 最后字符必须是字母数字
    const lastChar = trimmedPrefix[trimmedPrefix.length - 1];
    if (!lastChar || !isAlphanumeric(lastChar)) {
      return true;
    }

    // 检查6: 括号匹配
    const trimmedSuffix = currentLineSuffix.trim();
    if (
      !trimmedSuffix.endsWith("{") &&
      !trimmedSuffix.endsWith("(") &&
      !trimmedSuffix.endsWith("[")
    ) {
      // 如果不以开括号结尾，检查括号平衡
      const unpairedChars = findUnpairedAutoClosingChars(fullLine);
      if (unpairedChars.length > 0) {
        return true;
      }
    }

    // 如果没有触发任何中止条件，允许自动补全
    return false;
  }

  /**
   * 针对某个特定项目，进行初始化数据缓存建立
   */
  public async projectInitialize(
    projectPath: string,
    codeCompleteStrategy: string[] = ["rlcc", "bm25"]
  ): Promise<boolean> {
    try {
      logger.info(
        "[cf tabby]projectInitialize begin:" +
        projectPath +
        ",codeCompleteStrategy:" +
        codeCompleteStrategy
      );
      if (codeCompleteStrategy.includes("rlcc")) {
        // 开始rlcc扫描
        await this.startRlccServer(projectPath);
      }
      // 开始bm25扫描
      const delay = global.passingParameters.env === "prod" ? 600000 : 1000;
      if (codeCompleteStrategy.includes("bm25")) {
        // 延时10分钟（600000毫秒）再执行，避开rlcc并发扫描
        await new Promise<void>((resolve, reject) => {
          setTimeout(async () => {
            try {
              await this.startRagServer(projectPath);
              resolve();
            } catch (error) {
              reject(error);
            }
          }, delay);
        });
      }

      return true;
    } catch (error) {
      // 可选：记录错误日志
      logger.error(`[cf tabby]projectInitialize error:`, error);
      return false;
    }
  }

  /**
   * 启动rag服务
   * @param projectPath - 项目路径
   * @returns 是否启动成功
   */
  public async startRagServer(projectPath: string): Promise<boolean> {
    logger.info("[cf tabby]startRagServer begin:" + projectPath);
    this.ragService = new RagService(projectPath);
    return this.ragService.isEnable();
  }

  /**
   * 启动RLCC服务
   * @param projectPath - 项目路径
   * @returns 是否启动成功
   */
  public async startRlccServer(projectPath: string): Promise<boolean> {
    try {
      logger.info("[cf tabby]startRlccServer begin:" + projectPath);
      this.rlccService = ProjectCodeIndexer.getInstance(projectPath);
      return true;
    } catch (error) {
      logger.error("[cf tabby]Failed to start rlcc service.", error);
      return false;
    }
  }

  /**
   * 获取项目文件更新
   * @returns 是否更新成功
   */
  public async updateFiles(files: UpdatedFile[]): Promise<boolean> {
    // 此处处理文件列表逻辑
    logger.debug("[cf tabby]updateFiles: " + JSON.stringify(files));

    // 如果RLCC服务已启动，更新文件索引
    if (this.rlccService) {
      try {
        await this.rlccService.updateFiles(files);
        logger.debug("[cf tabby]RLCC service updated files successfully");
      } catch (error) {
        logger.error(
          "[cf tabby]Failed to update files in RLCC service:",
          error
        );
        return false;
      }
    }

    return true;
  }

  /**
   * rlcc对外测试接口
   * @param filePath - 文件路径
   * @param line - 行号
   * @param column - 列号
   * @returns 相关代码对象数组或null
   */
  public async findRelativeObject(
    fileContent: string,
    filePath: string,
    line: number,
    column: number
  ): Promise<RelativeCodeObject[] | null> {
    if (this.rlccService) {
      return this.rlccService.FindRelativeObject(fileContent,filePath, line, column);
    }
    return null;
  }

  /**
   * 提供代码补全
   * 处理补全请求并返回补全结果
   *
   * 处理流程：
   * 1. 检查缓存
   * 2. 处理防抖
   * 3. 发送补全请求
   * 4. 处理响应
   * 5. 更新统计数据
   *
   * @param request - 补全请求
   * @param options - 请求选项
   * @returns 补全响应
   */
  public async provideCompletions(
    request: CompletionRequest,
    options?: AbortSignalOption
  ): Promise<CompletionResponse> {
    // 记录请求开始时间和初始化时间戳
    const startTime = Date.now();
    const reqId = request.id || uuid();
    const logPrefix = `[cf tabby][cpl-${reqId}]`;

    // 初始化时间戳对象，用于记录各阶段耗时
    const timeTracker = this.initializeTimeTracker(startTime);

    // 记录请求接收时间和详情
    logger.debug(`${logPrefix} Request received at: ${startTime}`);
    logger.debug(`${logPrefix} Request : ${JSON.stringify(request)}`);

    // 处理请求并发控制，确保同时只有一个活跃请求
    const signal = this.setupRequestConcurrency(options);

    // 创建补全上下文对象，包含代码环境信息
    const context = new CompletionContext(request);

    try {
      // 处理补全请求并获取响应
      const completionResponse = await this.processCompletionRequest(
        context,
        request,
        signal,
        timeTracker,
        logPrefix
      );

      // 记录时间统计信息并完成请求
      this.logTimingStatistics(timeTracker, logPrefix);
      logger.debug(`[cf tabby]${logPrefix} Request completed`);

      return completionResponse;
    } catch (error) {
      // 错误处理，直接抛出异常
      throw error;
    }
  }

  /**
   * 初始化时间追踪器
   * 用于记录补全请求各阶段的时间戳
   *
   * @param startTime - 请求开始时间
   * @returns 时间追踪器对象
   * @private
   */
  private initializeTimeTracker(startTime: number) {
    return {
      startTime,
      beforeToServer: startTime,
      afterFromServer: startTime,
      ragStartTime: startTime,
      ragEndTime: startTime,
      preProcessStartTime: startTime,
      preProcessEndTime: startTime,
      postProcessStartTime: startTime,
      postProcessEndTime: startTime,
      endTime: startTime,
    };
  }

  /**
   * 设置请求并发控制
   * 取消之前未完成的补全请求，确保同时只有一个活跃请求
   *
   * @param options - 请求选项
   * @returns 合并后的中止信号
   * @private
   */
  private setupRequestConcurrency(options?: AbortSignalOption): AbortSignal {
    // 取消之前未完成的补全请求
    if (this.nonParallelProvideCompletionAbortController) {
      this.nonParallelProvideCompletionAbortController.abort();
    }

    // 创建新的中止控制器
    this.nonParallelProvideCompletionAbortController = new AbortController();

    // 合并内部中止信号和外部中止信号
    return abortSignalFromAnyOf([
      this.nonParallelProvideCompletionAbortController.signal,
      options?.signal,
    ]);
  }

  /**
   * 处理补全请求
   * 处理补全请求的主要逻辑，包括文本段处理、RAG检索和服务器请求
   *
   * @param context - 补全上下文
   * @param request - 补全请求
   * @param signal - 中止信号
   * @param timeTracker - 时间追踪器
   * @param logPrefix - 日志前缀
   * @returns 补全响应
   * @private
   */
  private async processCompletionRequest(
    context: CompletionContext,
    request: CompletionRequest,
    signal: AbortSignal,
    timeTracker: any,
    logPrefix: string
  ): Promise<CompletionResponse> {
    // 创建文本段（前缀、后缀等）
    const segments = this.createSegments(context);

    // 如果前缀为空，则返回空结果
    if (isBlank(segments.prefix)) {
      this.logger.debug(
        "Segment prefix is blank, returning empty completion response"
      );
      return {
        id: "agent-" + uuid(),
        choices: [],
      };
    }

    try {

      // 允许生效的补全策略，后端下发
      const codeCompleteStrategy = request.codeCompleteStrategy || [
        "rlcc",
        "bm25",
      ];
      let rlccResponse: { filepath: string; body: string; score: number }[] =
        [];
      let ragResponse: { filepath: string; body: string; score: number }[] = [];

      // 获取跨文件关联参考信息
      if (codeCompleteStrategy.includes("rlcc")) {
        rlccResponse = await this.fetchRlccResponse(request);
      }
      // 获取bm25参考信息
      if (codeCompleteStrategy.includes("bm25")) {
        ragResponse = await this.fetchRagResponse(
          request,
          segments,
          signal,
          timeTracker,
          logPrefix
        );
      }

      // 合并不同策略召回的参考上下文信息
      let combinedContext = this.combineContexts(rlccResponse, ragResponse);
      // 裁剪补全上下文数据，以免超过阈值长度
      const inputCharacterLimit = request.inputCharacterLimit ?? 13000;
      const snippetSizeLimit = request.snippetSizeLimit ?? 4000;
      // -- 前后缀没用完的额度，挪给snippet
      const maxLength =
        inputCharacterLimit - request.text.length + snippetSizeLimit;
      combinedContext = this.trimCombinedContext(combinedContext, maxLength);
      logger.debug(
        `${logPrefix} processCompletionRequest combinedContext: ${JSON.stringify(combinedContext)}`
      );
      // 发送补全请求到服务器并获取响应
      const completionResponse = await this.sendCompletionRequest(
        request,
        segments,
        combinedContext,
        signal,
        timeTracker,
        logPrefix
      );

      return completionResponse;
      // 处理响应（预处理和后处理）
      // return await this.processCompletionResponse(
      //   context,
      //   completionResponse,
      //   signal,
      //   timeTracker,
      //   logPrefix,
      //   request.id
      // );
    } catch (error) {
      throw error;
    }
  }

  private async fetchRlccResponse(request: CompletionRequest): Promise<
    {
      filepath: string;
      body: string;
      score: number;
    }[]
  > {
    // 初始化rlcc响应数组
    let rlccResponse: { filepath: string; body: string; score: number }[] = [];

    // 检查RLCC服务是否可用
    if (this.rlccService) {
      // 调用RLCC服务查找与当前光标位置相关的代码对象
      const relativeObjects = await this.rlccService.FindRelativeObject(
        request.text,
        request.filepath,
        request.line,
        request.col
      );

      // 检查是否找到相关对象
      if (relativeObjects && relativeObjects.length > 0) {
        // 将relativeObjects转换为rlccResponse格式
        rlccResponse = relativeObjects.map((obj) => ({
          filepath: obj.getCodeFilePath() || request.filepath, // 使用对象名称作为文件路径，如果为空则使用请求的文件路径
          body: obj.getObjectText() || "", // 使用简单文本作为内容
          score: 1.0,
        }));

        // 记录找到的相关对象内容
        logger.debug(
          `[RLCC] Found relative objects for completion:${request.filepath} at line ${request.line}, column ${request.col}`
        );
      } else {
        // 记录未找到相关对象的情况
        logger.debug(
          `[RLCC] No relative objects found for ${request.filepath} at line ${request.line}, column ${request.col}`
        );
      }
    }

    return rlccResponse;
  }

  /**
   * 获取RAG响应
   * 根据补全模式决定是否使用RAG，并发送RAG请求
   *
   * @param request - 补全请求
   * @param segments - 文本段
   * @param signal - 中止信号
   * @param timeTracker - 时间追踪器
   * @param logPrefix - 日志前缀
   * @returns RAG响应数组
   * @private
   */
  // todo:迁移到ragService里面
  private async fetchRagResponse(
    request: CompletionRequest,
    segments: { prefix: string; suffix: string; clipboard?: string },
    signal: AbortSignal,
    timeTracker: any,
    logPrefix: string
  ): Promise<{ filepath: string; body: string; score: number }[]> {
    // 初始化RAG响应数组
    let ragResponse: { filepath: string; body: string; score: number }[] = [];

    // 只有rag启动之后才会发起请求
    if (this.ragService == null || !this.ragService.isEnable()) {
      logger.info("未启动rag");
      return ragResponse;
    }

    // 根据补全策略模式决定是否使用RAG
    if (this.config.server.cplMode !== "速度优先") {
      // 使用RAG增强补全质量
      const url =
        "http://127.0.0.1:" +
        this.ragService.getRagPort() +
        "/v1/completionsWithRag";
      const data = {
        language: request.language,
        segments,
        id: this.ragService.getProjectId(),
      };

      // 记录RAG请求开始时间
      timeTracker.ragStartTime = Date.now();
      logger.debug(
        `${logPrefix} Starting RAG request at: ${timeTracker.ragStartTime}ms`
      );
      logger.debug(`${logPrefix} RAG request url: ${url}`);
      logger.debug(`${logPrefix} RAG request data: ${JSON.stringify(data)}`);

      // 发送RAG请求
      await fetchPost(
        url,
        data,
        5000,
        this.createAbortSignal({
          signal,
          timeout: this.config.completion.timeout,
        })
      )
        .then((response) => {
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          return response.json();
        })
        .then((data) => {
          // 处理RAG响应
          logger.debug(
            `${logPrefix} rag response for id ${request.id}: ` +
            "=" +
            JSON.stringify(data) +
            "="
          );
          ragResponse = data.snippets;
        })
        .catch((error) => {
          // 记录RAG错误
          logger.error(`${TabbyAgent.LOG_PREFIX} rag request error:`, error);
        });

      // 记录RAG请求结束时间
      timeTracker.ragEndTime = Date.now();
      logger.debug(
        `${logPrefix} RAG request completed in: ${timeTracker.ragEndTime - timeTracker.ragStartTime}ms`
      );
    }

    return ragResponse;
  }

  /**
   * 合并不同策略召回的参考上下文
   */
  private combineContexts(
    rlccResponse: { filepath: string; body: string; score: number }[],
    ragResponse: { filepath: string; body: string; score: number }[]
  ): { filePath: string; snippet: string; score: number; strategy: string }[] {
    const combinedContext: {
      filePath: string;
      snippet: string;
      score: number;
      strategy: string;
    }[] = [];
    // 先合并rlcc的内容
    rlccResponse.forEach((item) => {
      combinedContext.push({
        filePath: item.filepath,
        snippet: item.body,
        score: 100, // rlcc的参考内容得分最高
        strategy: "rlcc",
      });
    });

    // 再合并bm25的内容
    // 1、按分数排序RAG响应
    ragResponse.sort((a, b) => b.score - a.score);
    // 2、添加RAG片段
    ragResponse.forEach((item) => {
      combinedContext.push({
        filePath: item.filepath,
        snippet: item.body,
        score: item.score,
        strategy: "bm25",
      });
    });

    return combinedContext;
  }

  /**
   * 发送补全请求到服务器
   * 构建请求体并发送请求
   *
   * @param request - 补全请求
   * @param segments - 文本段
   * @param combinedContext - 合并的上下文
   * @param signal - 中止信号
   * @param timeTracker - 时间追踪器
   * @param logPrefix - 日志前缀
   * @returns 补全响应
   * @private
   */
  private async sendCompletionRequest(
    request: CompletionRequest,
    segments: { prefix: string; suffix: string; clipboard?: string },
    combinedContext: {
      filePath: string;
      snippet: string;
      score: number;
      strategy: string;
    }[],
    signal: AbortSignal,
    timeTracker: any,
    logPrefix: string
  ): Promise<CompletionResponse> {
    // 记录服务器请求开始时间
    timeTracker.beforeToServer = Date.now();
    logger.debug(
      `${logPrefix} Send server request at: ${timeTracker.beforeToServer}ms`
    );

    let response: Response;
    let responseData: any;
    let completionResponse: CompletionResponse;

    // 区分codefree和非codefree的请求逻辑
    if (global.passingParameters.serverType !== "codefree") {
      // 非codefree逻辑：使用 /v1/completions 路径
      const requestPath = "/v1/completions";

      // 判断是否为单行模式和自动模式
      const ifSingleMode = request.singleLine || false;
      const isAutoMode = !request.manually;

      // 设置请求选项，包含RAG结果
      const requestOptions = {
        body: {
          id: request.id,
          language: request.language,
          segments,
          snippets: combinedContext, // 使用合并后的上下文作为snippets
          user: global.passingParameters.invokerId, // 使用invokerId作为user
          single_mode: ifSingleMode,
          auto: isAutoMode,
          stopWords: request.stopWords
        },
        signal: this.createAbortSignal({
          signal,
          timeout: this.config.completion.timeout,
        }),
      };

      logger.debug(`${logPrefix} Non-codefree request body: ${JSON.stringify(requestOptions.body)}`);

      // 发送非codefree请求
      response = await fetchPost(
        `${global.passingParameters.serverBaseUrl}${requestPath}`,
        requestOptions.body,
        5000,
        requestOptions.signal,
        {
          'Content-Type': 'application/json'
        }
      );

      logger.debug(`${logPrefix} Non-codefree request URL: ${global.passingParameters.serverBaseUrl}${requestPath}`);
      logger.debug(`${logPrefix} Non-codefree received raw response: ${JSON.stringify(response)}`);

      // 检查响应状态
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // 解析响应数据
      responseData = await response.json();
      logger.debug(`${logPrefix} Non-codefree received raw responseData: ${JSON.stringify(responseData)}`);

      // 构建非codefree的补全响应对象
      completionResponse = {
        id: responseData.id || request.id,
        choices: responseData.choices || [],
      };

    } else {
      // codefree逻辑：保持原有逻辑
      const gitUrls = request.gitUrls || [];
      const codefreeRequestBody = {
        messageName: "CodeGenRequest",
        context: {
          messageName: "CodeGenRequest",
          reqId: request.id,
          invokerId: global.passingParameters.invokerId,
        version: global.passingParameters.pluginVersion,
        },
        payload: {
          clientType: global.passingParameters.clientType,
          clientVersion: global.passingParameters.clientVersion,
          gitUrls: gitUrls,
          messages: {
            filename: request.filepath,
            importSnippets: combinedContext,
            language: request.language,
            max_new_tokens: 256,
            prefix: segments.prefix,
            suffix: segments.suffix,
          stop_words: request.stopWords,
        },
      },
      };
    logger.debug(
      `${logPrefix} Send server request: ${JSON.stringify(codefreeRequestBody)}`
    );

      // 发送codefree请求
      response = await fetchPost(
        `${global.passingParameters.serverBaseUrl}/codegen/v1/completion`,
        codefreeRequestBody,
        5000,
        this.createAbortSignal({
          signal,
          timeout: this.config.completion.timeout,
        }),
        {
        userId: global.passingParameters.invokerId,
        apiKey: global.passingParameters.apiKey,
        "Content-Type": "application/json",
        }
      );
      logger.debug(`${logPrefix} Codefree request URL: ${global.passingParameters.serverBaseUrl}/codegen/v1/completion`);
      logger.debug(`${logPrefix} Codefree received raw response: ${JSON.stringify(response)}`);
    logger.debug(
      `${logPrefix} request raw param${JSON.stringify(`${global.passingParameters.serverBaseUrl}/v1/completion`)}`
    );
    logger.debug(
      `${logPrefix} received raw response${JSON.stringify(response)}`
    );

      // 检查响应状态
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // 解析响应数据
      responseData = await response.json();
      logger.debug(`${logPrefix} Codefree received raw responseData: ${JSON.stringify(responseData)}`);

      // 构建codefree的补全响应对象
      completionResponse = {
        id: responseData.reqId,
      choices: [
        {
          index: 0,
          text: responseData.answer,
          replaceRange: {
            start: request.position,
            end: request.position,
          },
        },
      ],
      };

      // 添加token_probs到响应数据
      if (responseData.token_probs && Array.isArray(responseData.token_probs)) {
        logger.debug(`${logPrefix} Codefree received token_probs: ${JSON.stringify(responseData.token_probs)}`);
        (completionResponse as any).token_probs = responseData.token_probs;
      }
    }

    // 记录服务器响应时间
    timeTracker.afterFromServer = Date.now();
    logger.debug(`${logPrefix} Server request completed in: ${timeTracker.afterFromServer - timeTracker.beforeToServer}ms`);

    return completionResponse;
  }

  /**
   * 处理补全响应
   * 对补全响应进行预处理和后处理
   *
   * @param context - 补全上下文
   * @param completionResponse - 补全响应
   * @param signal - 中止信号
   * @param timeTracker - 时间追踪器
   * @param logPrefix - 日志前缀
   * @param requestId - 请求ID
   * @returns 处理后的补全响应
   * @private
   */
  private async processCompletionResponse(
    context: CompletionContext,
    completionResponse: CompletionResponse,
    signal: AbortSignal,
    timeTracker: any,
    logPrefix: string,
    requestId?: string
  ): Promise<CompletionResponse> {
    // 对于非codefree服务器类型，进行预处理和后处理
    if (global.passingParameters.serverType !== "codefree") {
      // 预处理响应
      timeTracker.preProcessStartTime = Date.now();
      completionResponse = await preCacheProcess(context, this.config.postprocess, completionResponse, requestId);
      timeTracker.preProcessEndTime = Date.now();
      logger.debug(`${logPrefix} after pre-post-processing: ${JSON.stringify(completionResponse.choices[0]?.text)}`);

      // 检查是否被中止
      if (signal.aborted) {
        throw signal.reason;
      }

      // 后处理响应
      timeTracker.postProcessStartTime = Date.now();
      logger.debug(`${logPrefix} Starting post-processing at: ${timeTracker.postProcessStartTime}ms`);
      completionResponse = await postCacheProcess(context, this.config.postprocess, completionResponse, requestId);
      timeTracker.postProcessEndTime = Date.now();
      logger.debug(`${logPrefix} Post-processing completed in: ${timeTracker.postProcessEndTime - timeTracker.postProcessStartTime}ms`);
    }

    // 检查是否被中止
    if (signal.aborted) {
      throw signal.reason;
    }

    // 记录请求结束时间
    timeTracker.endTime = Date.now();

    return completionResponse;
  }

  /**
   * 记录时间统计信息
   * 生成并记录各阶段的时间统计信息
   *
   * @param timeTracker - 时间追踪器
   * @param logPrefix - 日志前缀
   * @private
   */
  private logTimingStatistics(timeTracker: any, logPrefix: string): void {
    // 生成时间统计摘要
    const timings = {
      total: timeTracker.endTime - timeTracker.startTime,
      rag: timeTracker.ragEndTime - timeTracker.ragStartTime,
      serverRequest: timeTracker.afterFromServer - timeTracker.beforeToServer,
      preProcess:
        timeTracker.preProcessEndTime - timeTracker.preProcessStartTime,
      postProcess:
        timeTracker.postProcessEndTime - timeTracker.postProcessStartTime,
    };

    // 记录时间统计信息
    logger.debug(
      `${logPrefix} Timing Summary: Total time: ${timings.total}ms, RAG time: ${timings.rag}ms, Server request time: ${timings.serverRequest}ms, Pre-processing time: ${timings.preProcess}ms, Post-processing time: ${timings.postProcess}ms`
    );
  }

  /**
   * 裁剪结构以适应最大长度限制
   * 智能调整文本段和参考代码片段的长度【综合裁剪】
   */
  trimCombinedContext(
    snippets: {
      filePath: string;
      snippet: string;
      score: number;
      strategy: string;
    }[],
    maxLength: number = 4000
  ) {
    // 计算当前结构的总字符长度
    function calculateTotalLength(
      snippets: { filePath: string; snippet: string }[]
    ): number {
      // 计算所有代码片段的总长度
      return snippets.reduce(
        (acc, snippet) =>
          acc + snippet.filePath.length + snippet.snippet.length,
        0
      );
    }

    // 长度超出限制，从snippets倒序进行裁剪（因为snippets按得分排序）
    let totalLength = calculateTotalLength(snippets);
    if (totalLength > maxLength) {
      while (totalLength > maxLength && snippets.length > 0) {
        const lastSnippet = snippets.pop();
        if (lastSnippet) {
          totalLength -= lastSnippet.snippet.length;
        }
      }
    }

    return snippets;
  }
}
