import hashObject from "object-hash";
import { regOnlyAutoClosingCloseChars, splitLines } from "./utils";

/**
 * 代码补全请求参数接口
 */
export type CompletionRequest = {
  /** 请求唯一标识符 */
  id?: string;
  /** 文件路径 */
  filepath: string;
  /** 编程语言标识符 */
  language: string;
  /** 完整的文本内容 */
  text: string;
  /** 光标位置 */
  position: number;
  /** 缩进字符串 */
  indentation?: string;
  /** 剪贴板内容 */
  clipboard?: string;
  /** 是否为手动触发 */
  manually?: boolean;
  /** 是否限制为单行补全 */
  singleLine?: boolean;
  /** 上下文信息数组 */
  context?: string[];
  /** 导入代码片段 */
  importSnippets?: importSnippets[];
  /** git remote列表 */
  gitUrls?: string[];
};

export type importSnippets = {
  filePath: string;
  snippet: string;
};

export type updatedFile = {
  filePath: string,
  updateType: 'change' | 'create' | 'delete'
}

/**
 * 补全响应选项接口
 */
export type CompletionResponseChoice = {
  /** 选项索引 */
  index: number;
  /** 补全文本 */
  text: string;
  /**
   * 需要替换的文本范围
   * 范围应限制在当前行内
   */
  replaceRange: {
    /** 起始位置 */
    start: number;
    /** 结束位置 */
    end: number;
  };
};

/**
 * 补全响应接口
 */
export type CompletionResponse = {
  /** 响应ID */
  id: string;
  /** 补全选项数组 */
  choices: CompletionResponseChoice[];
  /** 词元概率数据，用于ONNX质量预测 */
  token_probs?: any[];
};

/**
 * 检查是否在行尾(不包括自动闭合字符)
 * @param suffix - 后缀文本
 * @returns 是否在行尾
 * @example
 * func a {cursor} => returns true
 */
function isAtLineEndExcludingAutoClosedChar(suffix: string) {
  return suffix.trimEnd().match(regOnlyAutoClosingCloseChars);
}

/**
 * 补全上下文类
 * 处理和管理代码补全的上下文信息
 */
export class CompletionContext {
  /** 文件路径 */
  filepath: string;
  /** 编程语言 */
  language: string;
  /** 缩进字符串 */
  indentation?: string;
  /** 完整文本 */
  text: string;
  /** 光标位置 */
  position: number;

  /** 光标前的文本 */
  prefix: string;
  /** 光标后的文本 */
  suffix: string;
  /** 前缀文本按行分割 */
  prefixLines: string[];
  /** 后缀文本按行分割 */
  suffixLines: string[];
  /** 当前行光标前的文本 */
  currentLinePrefix: string;
  /** 当前行光标后的文本 */
  currentLineSuffix: string;
  /** 是否限制单行补全 */
  singleLine?: boolean;
  /** 剪贴板内容 */
  clipboard: string;

  /**
   * 补全模式
   * "default": 光标在行尾，不限制单行补全
   * "fill-in-line": 光标不在行尾，限制为单行补全
   */
  mode: "default" | "fill-in-line";
  /** 上下文哈希值 */
  hash: string;

  constructor(request: CompletionRequest) {
    this.filepath = request.filepath;
    this.language = request.language;
    this.text = request.text;
    this.position = request.position;
    this.indentation = request.indentation;
    this.singleLine = request.singleLine;
    this.prefix = request.text.slice(0, request.position);
    this.suffix = request.text.slice(request.position);
    this.prefixLines = splitLines(this.prefix);
    this.suffixLines = splitLines(this.suffix);
    this.currentLinePrefix = this.prefixLines[this.prefixLines.length - 1] ?? "";
    this.currentLineSuffix = this.suffixLines[0] ?? "";

    this.clipboard = request.clipboard?.trim() ?? "";

    // 确定补全模式
    const isFirstSuffixEndsWithAutoClosedChars = isAtLineEndExcludingAutoClosedChar(this.suffixLines[0] ?? "");
    this.mode = isFirstSuffixEndsWithAutoClosedChars ? "default" : "fill-in-line";

    // 生成上下文哈希值
    this.hash = hashObject({
      filepath: this.filepath,
      language: this.language,
      text: this.text,
      position: this.position,
      clipboard: this.clipboard,
    });
  }
}
